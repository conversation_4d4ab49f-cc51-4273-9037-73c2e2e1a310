name: CI

on:
  pull_request:
  push:
    branches:
      - main
      - 'releases/*'

permissions:
  contents: read

env:
  CI: true
  TZ: Asia/Shanghai

jobs:
  test:
    name: Test
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - ubuntu-latest
          # - macos-latest
          - windows-latest
    timeout-minutes: 20
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Setup Node
        uses: ./.github/actions/setup-node

      # - name: Check Git version
      #   run: git --version

      # - name: Setup mock Git user
      #   run: git config --global user.email "<EMAIL>" && git config --global user.name "Your Name"

      - name: Vitest tests
        run: pnpm run test:unit

      # - name: Upload coverage
      #   uses: codecov/codecov-action@v4
      #   with:
      #     token: ${{ secrets.CODECOV_TOKEN }}

  lint:
    name: Lint
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - ubuntu-latest
          # - macos-latest
          - windows-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Lint
        run: pnpm run lint

  check:
    name: Check
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ${{ matrix.os }}
    timeout-minutes: 20
    strategy:
      matrix:
        os:
          - ubuntu-latest
          # - macos-latest
          - windows-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Typecheck
        run: pnpm check:type

      # From https://github.com/rhysd/actionlint/blob/main/docs/usage.md#use-actionlint-on-github-actions
      - name: Check workflow files
        if: runner.os == 'Linux'
        run: |
          bash <(curl https://raw.githubusercontent.com/rhysd/actionlint/main/scripts/download-actionlint.bash)
          ./actionlint -color -shellcheck=""

  ci-ok:
    name: CI OK
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ubuntu-latest
    needs: [test, check, lint]
    env:
      FAILURE: ${{ contains(join(needs.*.result, ','), 'failure') }}
    steps:
      - name: Check for failure
        run: |
          echo $FAILURE
          if [ "$FAILURE" = "false" ]; then
            exit 0
          else
            exit 1
          fi
