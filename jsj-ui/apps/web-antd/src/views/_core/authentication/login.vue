<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { BasicOption } from '@vben/types';

import { computed, markRaw } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationLogin, SliderCaptcha, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });
const router = useRouter();
const authStore = useAuthStore();

const MOCK_USER_OPTIONS: BasicOption[] = [
    {
        label: 'Super',
        value: 'vben',
    },
    {
        label: 'Admin',
        value: 'admin',
    },
    {
        label: 'User',
        value: 'jack',
    },
];

const formSchema = computed((): VbenFormSchema[] => {
    return [
        // {
        //   component: 'VbenSelect',
        //   componentProps: {
        //     options: MOCK_USER_OPTIONS,
        //     placeholder: $t('authentication.selectAccount'),
        //   },
        //   fieldName: 'selectAccount',
        //   label: $t('authentication.selectAccount'),
        //   rules: z
        //     .string()
        //     .min(1, { message: $t('authentication.selectAccount') })
        //     .optional()
        //     .default('vben'),
        // },
        {
            component: 'VbenInput',
            componentProps: {
                placeholder: '请输入手机号',
            },
            // dependencies: {
            //   trigger(values, form) {
            //     if (values.selectAccount) {
            //       form.setValues({
            //         password: '123456',
            //         username: '',
            //       });
            //     }
            //   },
            //   triggerFields: ['selectAccount'],
            // },
            defaultValue: '',
            fieldName: 'username',
            label: $t('authentication.username'),
            rules: z
                .string()
                .min(1, { message: $t('authentication.mobileTip') })
                .refine((v) => /^\d{11}$/.test(v), {
                    message: $t('authentication.mobileErrortip'),
                }),
        },
        {
            component: 'VbenInputPassword',
            componentProps: {
                placeholder: $t('authentication.password'),
            },
            defaultValue: '',
            fieldName: 'password',
            label: $t('authentication.password'),
            rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
        },
        // {
        //   component: markRaw(SliderCaptcha),
        //   fieldName: 'captcha',
        //   rules: z.boolean().refine((value) => value, {
        //     message: $t('authentication.verifyRequiredTip'),
        //   }),
        // },
    ];
});
//
const goRegister = () => {
    router.push('/auth/version');
};
</script>

<template>
    <div>
        <AuthenticationLogin
            :form-schema="formSchema"
            :show-code-login="false"
            :show-qrcode-login="false"
            :show-third-party-login="false"
            :show-register="false"
            :show-forget-password="false"
            :loading="authStore.loginLoading"
            @submit="authStore.authLogin"
        />
        <div class="mt-3">
            <span
                @click="goRegister"
                style="cursor: pointer"
                class="hover:text-green-500"
            >
                开通账户
            </span>
        </div>
        <!-- <a-button style="width: 100%; margin-top: 30px"> 开通账户 </a-button> -->
    </div>
</template>
