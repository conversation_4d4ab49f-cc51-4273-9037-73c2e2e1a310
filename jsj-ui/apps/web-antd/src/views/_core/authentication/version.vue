<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import Icon, { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';

const router = useRouter();
const state = reactive<{ list: any[] }>({
    list: [
        {
            name: '财务服务机构标准版',
            describe: '主要适用财税服务机构会计记账；包含功能：账薄管理、人员管理、凭证制作及管理、自动报表',
            id: 7,
            price: 0,
            priceText: '免费',
            purchaseQuantity: 1, // 购买数量
            purchasePeriod: 1, // 购买年限
            createAccounting: 3000, // 可建账簿
            isSelected: true, // 是否选中
            totalPrice: 0,
        },
    ],
});
// 下一步
const nextStep = () => {
    router.push({
        path: '/auth/register',
        query: {
            name: state.list[0].name,
            id: state.list[0].id,
        },
    });
};
</script>
<template>
    <div class="cont">
        <div class="tit">选择您所需要的版本</div>
        <div class="listbox">
            <ul
                class="item"
                :class="[item.isSelected ? 'on' : '']"
                v-for="(item, i) in state.list"
                :key="i"
            >
                <li class="li1">
                    <a-radio v-model:checked="item.isSelected" />
                </li>
                <li class="li2">
                    <div class="txt1">{{ item.name }}</div>
                    <div class="txt2">
                        <div
                            class="t1"
                            v-if="item.price === 0"
                        >
                            {{ item.priceText }}
                        </div>
                        <div
                            class="t2"
                            v-if="item.price > 0"
                        >
                            {{ item.priceText }}
                        </div>
                    </div>
                    <div class="txt3">{{ item.describe }}</div>
                    <div
                        class="btns"
                        v-if="item.price > 0"
                    >
                        <div>
                            <span class="s1">购买数量</span>
                            <MinusCircleOutlined class="s3" />
                            <span class="s2">{{ item.purchaseQuantity }}</span>
                            <PlusCircleOutlined class="s4" />
                        </div>
                        &nbsp;&nbsp;
                        <div>
                            <span class="s1">购买年限</span>
                            <MinusCircleOutlined class="s3" />
                            <span class="s2">{{ item.purchasePeriod }}</span>
                            <PlusCircleOutlined class="s4" />
                        </div>
                    </div>
                    <div class="txt4 mt-1">
                        <div>
                            <span class="s1">可建账簿：</span><span class="s2">{{ item.createAccounting }} </span><span class="s3">本</span>
                        </div>
                        <div class="ml-10">
                            <span class="s1">总价： </span><span class="s4">￥ {{ item.totalPrice }}</span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <a-button
            type="primary"
            style="width: 200px; margin-top: 20px"
            @click="nextStep"
        >
            下一步
        </a-button>
    </div>
</template>
<style lang="scss" scoped>
.cont {
    .tit {
        font-size: 14px;
        font-weight: bold;
        line-height: 40px;
    }

    .listbox {
        min-height: 400px;
        font-size: 12px;
        color: #606972;

        .item {
            display: flex;
            flex-direction: row;
            padding: 10px 0;
            margin-top: 10px;
            margin-bottom: 10px;
            border: solid 1px #eee;
            border-radius: 10px;

            &.on {
                border: solid 1px #0051ff;
            }

            .li1 {
                width: 50px;
                text-align: center;
            }

            .li2 {
                flex: 1;
            }

            .txt1 {
                font-weight: bold;
            }

            .txt2 {
                margin-top: 5px;
                color: #fff;

                .t1 {
                    display: inline-block;
                    padding: 0 5px;
                    line-height: 20px;
                    text-align: center;
                    background-color: #0051ff;
                }

                .t2 {
                    display: inline-block;
                    padding: 0 5px;
                    line-height: 20px;
                    text-align: center;
                    background-color: #ffcc67;
                }
            }

            .txt3 {
                margin-top: 5px;
                margin-right: 5px;
                color: #333;
            }

            .btns {
                overflow: hidden;
                color: #333;

                div {
                    display: inline-block;
                }

                .s1 {
                    font-weight: bold;
                }

                .s2 {
                    display: inline-block;
                    padding: 0 5px;
                    font-size: 14px;
                }

                .s3 {
                    margin-left: 8px;
                    font-size: 14px;
                    color: red;
                    cursor: pointer;
                }

                .s4 {
                    font-size: 14px;
                    color: green;
                    cursor: pointer;
                }
            }

            .txt4 {
                display: flex;
                flex-direction: row;

                .s1 {
                    font-weight: bold;
                    color: #333;
                }

                .s2 {
                    font-weight: bold;
                    color: #0051ff;
                }

                .s3 {
                    font-weight: bold;
                    color: #0051ff;
                }

                .s4 {
                    font-weight: bold;
                    color: #0051ff;
                }
            }
        }
    }
}
</style>
