<script lang="ts" setup>
import type { RegisterData } from '#/api/core/auth';

import { computed, h, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { getPrimaryUnit, getSecondaryMarket, register, registerverify, sendSMS, verificationIsCorrect } from '#/api/core/auth';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';
import { uTgetImgCodeUrl } from '#/utils/index';

import Agreement from './agreement.vue';

defineOptions({ name: 'Register' });

const useLoading = useGlobalLoading();

const route = useRoute();
const router = useRouter();
console.log(route.query);
const codeimgurl = ref<string>(uTgetImgCodeUrl());
const citiesOps1 = ref<any[]>([]); // 一级地理单位数组
const citiesOps2 = ref<any[]>([]); // 二级地理单位数组
const citiesOps3 = ref<any[]>([]); // 三级地理单位数组
const citiesValue1 = ref<string>(); // 1级
const citiesValue2 = ref<string>(); // 2级
const citiesValue3 = ref<string>(); // 3级
const formRef = ref<any>(null);
const msgCodeText = ref<string>('获取验证码'); // 获取短信验证码文字区域文字
const formState = reactive<any>({
    orgName: '', // 机构名称
    contactName: '', // 机构联系人
    contactPhone: '', // 联系人手机号
    password: '', // 密码
    areaCode: '', // 地理位置填写
    extendCode: '', // 推广码
    imgCode: '', // 图片验证码
    messageCode: '', // 手机号验证码
    isAgreement: true, // 是否同意用户协议
});
const [Drawer, drawerApi] = useVbenDrawer({
    // 连接抽离的组件
    connectedComponent: Agreement,
});
const onSumit = () => {
    formRef.value.validate().then((data: any) => {
        if (!formState.areaCode) {
            message.warning('地理位置请完成选择');
            return;
        }
        console.log(999, data);
        if (!formState.isAgreement) {
            message.warning('请阅读并同意《用户服务协议》');
            return;
        }
        useLoading.setShow(true);
        const regdata = {
            type: 1,
            areaCode: formState.areaCode,
            contactName: formState.contactName,
            contactPhone: formState.contactPhone,
            password: formState.password,
            imgCode: '',
            isAgreement: true,
            extendCode: formState.extendCode,
            count: 1,
            companyName: formState.orgName,

            // tenantId: '',
            // instanceAddress: '',
            username: formState.contactPhone,
            packageId: route.query.id,
        };
        register(regdata)
            .then((regres) => {
                console.log('注册的时候返回值', regres);
                useLoading.setShow(false);
                if (regres.returnCode === '200') {
                    router.push('/auth/login');
                    message.success('注册成功');
                } else {
                    message.error(regres.returnMsg);
                }
            })
            .catch(() => {
                useLoading.setShow(false);
            });
        return;
        const todata: RegisterData = {
            areaCode: formState.areaCode,
            contactName: formState.contactName,
            contactPhone: formState.contactPhone,
            count: 1, // TODO不知道啥意思 1
            extendCode: formState.extendCode,
            // imgCode: string; // 图形验证码  不要了
            isAgreement: formState.isAgreement, // 是否同意用户协议
            // messageCode: string; // 手机号验证码
            orgName: formState.orgName, // 机构名称
            packageId: 6, // TODO不知道啥意思 6
            type: 1, // TODO不知道啥意思 1
        };
        // 数据验证接口
        registerverify(todata)
            .then((res) => {
                console.log('数据验证返回', res);
                if (res.returnCode !== '200') {
                    message.warning(res.returnMsg);
                    useLoading.setShow(false);
                    return;
                }
                // 验证通过后请求注册接口
                // Integer type;
                // String areaCode;
                // String contactName; // 联系人名称
                // String contactPhone;    // 联系人手机号
                // String password;        // 管理员密码
                // String imgCode;
                // Boolean isAgreement;
                // Integer extendCode;
                // Integer count;
                // String companyName;

                // String tenantId;
                // String instanceAddress;
                // String username;   // 管理员账号
                // String packageId;
                const regdata = {
                    type: 1,
                    areaCode: formState.areaCode,
                    contactName: formState.contactName,
                    contactPhone: formState.contactPhone,
                    password: formState.password,
                    imgCode: '',
                    isAgreement: true,
                    extendCode: formState.extendCode,
                    count: 1,
                    companyName: formState.orgName,

                    // tenantId: '',
                    // instanceAddress: '',
                    username: formState.contactPhone,
                    packageId: route.query.id,
                };
                register(regdata)
                    .then((regres) => {
                        console.log('注册的时候返回值', regres);
                        useLoading.setShow(false);
                        if (regres.returnCode === '200') {
                            router.push('/auth/login');
                            message.success('注册成功');
                        } else {
                            message.warning(res.returnMsg);
                        }
                    })
                    .catch(() => {
                        useLoading.setShow(false);
                    });
            })
            .catch(() => {
                useLoading.setShow(false);
            });
    });
};
// 刷新图片验证码的方法
const imgCodeChange = () => {
    codeimgurl.value = uTgetImgCodeUrl();
};
const areaChange = (code: string, data: any, level: number) => {
    if (level < 3) {
        // 获取下一级位置数据
        getSecondaryMarket(code).then((res) => {
            if (res.returnCode === '200') {
                if (level === 1) {
                    citiesOps2.value = res.data;
                    citiesValue2.value = undefined;
                    citiesValue3.value = undefined;
                } else if (level === 2) {
                    citiesOps3.value = res.data;
                    citiesValue3.value = undefined;
                }
            }
        });
    }
};
const verificationCode: any = {
    num: 60, // 倒计时时间
    remaining: 0, // 倒计时剩余时间
    disable: false, // 时候禁用点击
    time: null,
};
const showCodeDownTime = () => {
    verificationCode.time && clearInterval(verificationCode.time);
    verificationCode.remaining = verificationCode.num;
    msgCodeText.value = `${verificationCode.remaining}s`;
    verificationCode.time = setInterval(() => {
        verificationCode.remaining--;
        if (verificationCode.remaining < 0) {
            clearInterval(verificationCode.time);
            msgCodeText.value = '获取验证码';
            verificationCode.disable = false;
        } else {
            msgCodeText.value = `${verificationCode.remaining}s`;
        }
    }, 1000);
};
// 获取验证码的方法
const getVerificationCode = () => {
    if (verificationCode.disable) {
        return;
    }
    formRef.value.validate(['contactPhone', 'imgCode']).then((data: any) => {
        verificationCode.disable = true;
        verificationIsCorrect(data.imgCode).then((res) => {
            if (res.returnCode !== '200') {
                message.warning(res.returnMsg);
                verificationCode.disable = false;
                return;
            }
            // 图像验证码过去了 发送短信
            sendSMS(data.contactPhone).then((coderes) => {
                if (coderes.returnCode === '200') {
                    // 发送短信成功
                    message.success('短信发送成功');
                    // 验证码倒计时处理
                    showCodeDownTime();
                } else {
                    message.warning(res.returnMsg);
                    verificationCode.disable = false;
                }
            });
        });
    });
};
onMounted(() => {
    // 获取地理位置
    getPrimaryUnit().then((res) => {
        if (res.returnCode === '200') {
            citiesOps1.value = res.data;
        }
    });
});
onUnmounted(() => {
    verificationCode.time && clearInterval(verificationCode.time);
});
watch([citiesValue1, citiesValue2, citiesValue3], ([value1, value2, value3]: any) => {
    if (value1 && value2 && value3) {
        // 查询位置
        const data1 = citiesOps1.value.find((v) => v.code === value1);
        const data2 = citiesOps2.value.find((v) => v.code === value2);
        const data3 = citiesOps3.value.find((v) => v.code === value3);
        formState.areaCode = `${data1.name} ${data2.name} ${data3.name}`;
    } else {
        formState.areaCode = '';
    }
});
</script>

<template>
    <div class="cont">
        <div class="tit1">请准确填写信息</div>
        <div class="tit2">{{ route.query.name }}</div>
        <div>
            <a-form
                ref="formRef"
                :model="formState"
                name="basic"
            >
                <a-form-item
                    name="orgName"
                    :rules="[
                        { required: true, message: '请输入机构名称' },
                        { max: 30, message: '不能超过30字' },
                    ]"
                >
                    <a-input
                        v-model:value="formState.orgName"
                        placeholder="机构名称不超过30字"
                    />
                </a-form-item>
                <a-form-item
                    name="contactName"
                    :rules="[{ required: true, message: '请填写机构联系人' }]"
                >
                    <a-input
                        v-model:value="formState.contactName"
                        placeholder="请填写机构联系人"
                    />
                </a-form-item>
                <a-form-item
                    name="contactPhone"
                    :rules="[{ required: true, message: '请输入正确的手机号' }]"
                >
                    <a-input
                        v-model:value="formState.contactPhone"
                        placeholder="请输入正确的手机号"
                    />
                </a-form-item>
                <a-form-item>
                    <div class="arebox">
                        <a-select
                            class="use-select"
                            placeholder="请选择"
                            v-model:value="citiesValue1"
                            :options="citiesOps1"
                            @change="
                                (value: string, data: any) => {
                                    areaChange(value, data, 1);
                                }
                            "
                            :field-names="{
                                label: 'name',
                                value: 'code',
                            }"
                        />
                        <a-select
                            class="use-select"
                            placeholder="请选择"
                            v-model:value="citiesValue2"
                            :options="citiesOps2"
                            @change="
                                (value: string, data: any) => {
                                    areaChange(value, data, 2);
                                }
                            "
                            :field-names="{
                                label: 'name',
                                value: 'code',
                            }"
                        />
                        <a-select
                            class="use-select"
                            placeholder="请选择"
                            v-model:value="citiesValue3"
                            :options="citiesOps3"
                            @change="
                                (value: string, data: any) => {
                                    areaChange(value, data, 3);
                                }
                            "
                            :field-names="{
                                label: 'name',
                                value: 'code',
                            }"
                        />
                    </div>
                </a-form-item>
                <a-form-item
                    name="password"
                    :rules="[
                        { required: true, message: '请输入密码' },
                        { min: 6, message: '密码不能低于6位' },
                        { max: 10, message: '密码不能大于10位' },
                    ]"
                >
                    <a-input-password
                        v-model:value="formState.password"
                        placeholder="请输入密码"
                    />
                </a-form-item>
                <!-- <a-form-item
                    name="imgCode"
                    :rules="[{ required: true, message: '请输入图形验证码' }]"
                >
                    <a-input
                        v-model:value="formState.imgCode"
                        placeholder="请输入图形验证码"
                    >
                        <template #addonAfter>
                            <span class="codeimg">
                                <img
                                    @click="imgCodeChange"
                                    :src="codeimgurl"
                                />
                            </span>
                        </template>
                    </a-input>
                </a-form-item> -->
                <!-- <a-form-item
                    name="messageCode"
                    :rules="[{ required: true, message: '请输入短信验证码' }]"
                >
                    <a-input
                        v-model:value="formState.messageCode"
                        placeholder="请输入短信验证码"
                    >
                        <template #addonAfter>
                            <span
                                class="codeimg"
                                @click="getVerificationCode"
                            >
                                {{ msgCodeText }}
                            </span>
                        </template>
                    </a-input>
                </a-form-item> -->
                <a-form-item>
                    <a-input
                        v-model:value="formState.extendCode"
                        placeholder="推广码(选填)"
                    />
                </a-form-item>
                <a-form-item>
                    <a-checkbox v-model:checked="formState.isAgreement"> 我已阅读并同意 </a-checkbox>
                    <span
                        class="xieyi"
                        @click.stop="drawerApi.open()"
                    >
                        《用户服务协议》
                    </span>
                </a-form-item>
            </a-form>
            <a-button
                type="primary"
                style="width: 100%; margin-bottom: 10px"
                @click="onSumit"
            >
                提交注册
            </a-button>
            <div
                style="display: inline-block; margin-bottom: 100px; font-size: 12px; cursor: pointer"
                @click="
                    () => {
                        router.push('/auth/login');
                    }
                "
            >
                回到登录页
            </div>
        </div>
        <Drawer
            placement="left"
            :footer="false"
        />
    </div>
</template>
<style lang="scss" scoped>
.arebox {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.cont {
    .tit1 {
        font-size: 18px;
        font-weight: bold;
        line-height: 20px;
        text-align: center;
    }

    .xieyi {
        color: #337ab7;
        cursor: pointer;
    }

    .codeimg {
        display: inline-block;
        width: 100px;
        height: 25px;
        line-height: 26px;
        cursor: pointer;
    }

    .tit2 {
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 14px;
        line-height: 26px;
        text-align: center;
    }
}

.use-select {
    width: 30%;
}
</style>
