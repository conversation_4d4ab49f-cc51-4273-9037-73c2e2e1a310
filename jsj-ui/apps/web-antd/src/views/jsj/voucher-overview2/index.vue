<script setup lang="ts">
  import type { Voucher } from './types';

  import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';

  import {
    DeleteOutlined,
    MinusOutlined,
    MoreOutlined,
    PlusOutlined,
    RightOutlined,
    SaveOutlined,
    SearchOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  import {
    getCurrentVouchers,
    getVoucherSourceData,
    mergeVouchers,
    updateVoucher,
  } from '#/api/tool/voucher';

  import SourceDataDetail from './components/SourceDataDetail.vue';

  const route = useRoute();

  // 响应式数据
  const vouchers = ref<Voucher[]>([]);
  const searchText = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const jumpPage = ref(1);
  const detailModalVisible = ref(false);
  const selectedVoucher = ref<null | Voucher>(null);
  const detailData = ref<any>(null);
  const detailLoading = ref(false);
  const loading = ref(false);
  const hasGeneratedVoucher = ref(false);
  const selectedVoucherIds = ref<Set<number | string>>(new Set());
  const mergeConfirmVisible = ref(false);
  const mergeLoading = ref(false);
  const filePreviewVisible = ref(false);
  const previewUrl = ref('');
  const previewTitle = ref('');
  const previewFiles = ref<any[]>([]);
  const currentFileIndex = ref(0);

  // 从路由参数获取公司和月份信息
  const companyName = ref(route.query.company as string || '');
  const month = ref(route.query.month as string || '');

  // 文件预览相关函数
  function previewFile(url: string, title: string = '') {
    previewUrl.value = url;
    previewTitle.value = title || '文件预览';
    filePreviewVisible.value = true;
  }

  function downloadFile(url: string, filename: string = '') {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || url.split('/').pop() || 'download';
    link.target = '_blank';
    document.body.append(link);
    link.click();
    link.remove();
  }

  function closeFilePreview() {
    filePreviewVisible.value = false;
    previewUrl.value = '';
    previewTitle.value = '';
    previewFiles.value = [];
    currentFileIndex.value = 0;
  }

  // 直接预览单个文件
  function handlePreviewFile(voucher: Voucher, fileIndex: number) {
    const files = getVoucherFiles(voucher);
    if (files.length > 0 && fileIndex < files.length) {
      previewFiles.value = files;
      currentFileIndex.value = fileIndex;
      previewFile(files[fileIndex].fullUrl, files[fileIndex].displayName);
    }
  }

  function getFileName(url: string): string {
    return url.split('/').pop() || '未知文件';
  }

  function getFileType(url: string): string {
    const ext = url.split('.').pop()?.toLowerCase() || '';
    if (['gif', 'jpeg', 'jpg', 'png', 'webp'].includes(ext)) {
      return 'image';
    } else if (ext === 'pdf') {
      return 'pdf';
    } else {
      return 'other';
    }
  }

  // 时间格式化函数
  function formatTimestamp(timestamp: string | undefined): string {
    if (!timestamp) return '';

    try {
      const isNumericTimestamp = /^\d+$/.test(timestamp);
      const date = isNumericTimestamp
        ? new Date(Number.parseInt(timestamp) * 1000)
        : new Date(timestamp);

      if (Number.isNaN(date.getTime())) {
        return timestamp;
      }

      return date.toLocaleString('zh-CN', {
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        month: '2-digit',
        second: '2-digit',
        year: 'numeric',
      });
    } catch (error) {
      console.warn('时间格式化失败:', error);
      return timestamp || '';
    }
  }

  // 处理银行回单信息，兼容对象和数组格式
  function getBankReceiptInfo(voucher: Voucher) {
    const bankReceiptInfo = voucher.source_info?.bank_receipt_info;
    if (!bankReceiptInfo) return null;

    // 如果是数组格式（合并的凭证）
    if (Array.isArray(bankReceiptInfo)) {
      let totalIncomeAmount = 0;
      let totalExpenseAmount = 0;
      let incomeTransactionNum = 0;
      let expenseTransactionNum = 0;
      let latestTimestamp = '';

      bankReceiptInfo.forEach((receipt) => {
        const amount = receipt.amount || 0;
        if (amount > 0) {
          totalIncomeAmount += amount;
          incomeTransactionNum++;
        } else if (amount < 0) {
          totalExpenseAmount += Math.abs(amount);
          expenseTransactionNum++;
        }

        if (
          receipt.transaction_time &&
          receipt.transaction_time > latestTimestamp
        ) {
          latestTimestamp = receipt.transaction_time;
        }
      });

      return {
        expense_transaction_num: expenseTransactionNum,
        income_transaction_num: incomeTransactionNum,
        is_merged: true,
        merged_count: bankReceiptInfo.length,
        timestamp: latestTimestamp || new Date().toISOString(),
        total_expense_amount: totalExpenseAmount,
        total_income_amount: totalIncomeAmount,
      };
    }

    // 如果是对象格式（普通凭证）
    return {
      ...bankReceiptInfo,
      is_merged: false,
      merged_count: 1,
    };
  }

  // 计算最佳页面大小
  function calculateOptimalPageSize(): number {
    const windowHeight = window.innerHeight;
    const headerHeight = 120;
    const footerHeight = 80;
    const tableHeaderHeight = 40;
    const voucherRowHeight = 120;

    const availableHeight =
      windowHeight - headerHeight - footerHeight - tableHeaderHeight;
    const estimatedRows = Math.floor(availableHeight / voucherRowHeight);

    return Math.max(5, Math.min(50, estimatedRows));
  }

  // 智能分页大小选项
  const smartPageSizeOptions = computed(() => {
    const optimal = calculateOptimalPageSize();
    const options = [
      { label: '10 条/页', value: 10 },
      { label: '20 条/页', value: 20 },
      { label: '30 条/页', value: 30 },
      { label: '50 条/页', value: 50 },
      { label: '100 条/页', value: 100 },
    ];

    if (options.some((opt) => opt.value === optimal)) {
      const optimalOption = options.find((opt) => opt.value === optimal);
      if (optimalOption) {
        optimalOption.label = `${optimal} 条/页 (推荐)`;
      }
    } else {
      options.splice(2, 0, {
        label: `${optimal} 条/页 (推荐)`,
        value: optimal,
      });
    }

    return options.sort((a, b) => a.value - b.value);
  });

  // 编辑状态管理
  const editingDetail = ref<null | {
    detailIndex: number;
    voucherId: number | string;
  }>(null);
  const editingField = ref<null | {
    detailIndex: number;
    field: string;
    voucherId: number | string;
  }>(null);

  const isSwitchingField = ref(false);

  // 取消编辑
  function cancelEdit() {
    console.log('取消编辑');
    editingField.value = null;
    editingDetail.value = null;
  }

  // 开始编辑字段
  async function startEditField(
    voucherId: number | string,
    detailIndex: number,
    field: string,
  ) {
    isSwitchingField.value = true;

    editingField.value = {
      detailIndex,
      field,
      voucherId,
    };

    await nextTick();

    const inputElement = document.querySelector(
      '.editing-field input',
    ) as HTMLInputElement;
    const numberInputElement = document.querySelector(
      '.editing-field .ant-input-number-input',
    ) as HTMLInputElement;

    if (inputElement) {
      inputElement.focus();
      inputElement.select();
    } else if (numberInputElement) {
      numberInputElement.focus();
      numberInputElement.select();
    }

    setTimeout(() => {
      isSwitchingField.value = false;
    }, 200);
  }

  // 完成编辑
  function finishEdit() {
    editingField.value = null;
  }

  // 处理输入框的键盘事件
  function handleInputKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      event.preventDefault();
      event.stopPropagation();
      cancelEdit();
    } else if (event.key === 'Enter') {
      event.preventDefault();
      event.stopPropagation();
      finishEdit();
    }
  }

  // 处理输入框失去焦点
  function handleInputBlur() {
    if (isSwitchingField.value) {
      return;
    }

    setTimeout(() => {
      if (editingField.value && !isSwitchingField.value) {
        const activeElement = document.activeElement;
        const isEditableElement =
          activeElement &&
          (activeElement.classList.contains('ant-input') ||
            activeElement.classList.contains('ant-input-number-input') ||
            activeElement.closest('.editing-field') ||
            activeElement.closest('.summary-text') ||
            activeElement.closest('.account-text') ||
            activeElement.closest('.amount-text'));

        if (!isEditableElement) {
          finishEdit();
        }
      }
    }, 150);
  }

  // 全局键盘事件处理
  function handleGlobalKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' && editingField.value) {
      console.log('全局ESC键取消编辑');
      cancelEdit();
      event.preventDefault();
      event.stopPropagation();
    }
  }

  // 从API加载凭证数据
  async function loadVouchersFromAPI() {
    try {
      loading.value = true;

      console.log('加载凭证数据:', { companyName: companyName.value, month: month.value });

      const response = await getCurrentVouchers(companyName.value, month.value);

      console.log('API响应:', response);

      // 转换API数据为组件所需的格式
      const apiVouchers = (response as any).data.items.map(
        (item: any, index: number) => ({
          confirmed: item.confirmed,
          details: item.voucher.details.map((detail: any) => ({
            account: detail.account,
            credit: detail.credit,
            debit: detail.debit,
            summary: detail.summary,
          })),
          executor: item.executor,
          id: item.voucher.unique_id || `voucher_${index}`,
          is_generated: false,
          originId: item.voucher.id,
          record_date: item.voucher.record_date,
          reviewed: item.confirmed,
          source_info: item.source_info,
          source_type: item.source_type,
          type: item.voucher.type,
        }),
      );

      // 按创建时间或ID降序排列
      const sortedVouchers = apiVouchers.sort((a: any, b: any) => {
        if (a.record_date && b.record_date) {
          return (
            new Date(b.record_date).getTime() -
            new Date(a.record_date).getTime()
          );
        }
        return String(b.id).localeCompare(String(a.id));
      });

      vouchers.value = sortedVouchers;
      message.success(`成功加载 ${apiVouchers.length} 条凭证数据`);
    } catch (error) {
      console.error('加载凭证数据失败:', error);
      message.error('加载凭证数据失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 计算属性
  const totalVouchers = computed(() => vouchers.value.length);
  const totalPages = computed(() =>
    Math.ceil(totalVouchers.value / pageSize.value),
  );

  const paginatedVouchers = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return vouchers.value.slice(start, end);
  });

  // 合并相关计算属性
  const selectedVouchers = computed(() => {
    return vouchers.value.filter((voucher) =>
      selectedVoucherIds.value.has(voucher.id),
    );
  });

  const canMergeVouchers = computed(() => {
    const selected = selectedVouchers.value;
    if (selected.length < 2) return false;
    return selected.every((voucher) => voucher.source_type === '银行回单');
  });

  const isAllSelected = computed(() => {
    const currentPageVouchers = paginatedVouchers.value;
    return (
      currentPageVouchers.length > 0 &&
      currentPageVouchers.every((voucher) =>
        selectedVoucherIds.value.has(voucher.id),
      )
    );
  });

  const isIndeterminate = computed(() => {
    const currentPageVouchers = paginatedVouchers.value;
    const selectedCount = currentPageVouchers.filter((voucher) =>
      selectedVoucherIds.value.has(voucher.id),
    ).length;
    return selectedCount > 0 && selectedCount < currentPageVouchers.length;
  });

  // 显示凭证详情
  async function showVoucherDetail(voucher: Voucher) {
    try {
      detailLoading.value = true;
      selectedVoucher.value = voucher;
      detailModalVisible.value = true;

      // 获取详细数据
      const response = await getVoucherSourceData(
        companyName.value,
        month.value,
        voucher.id as string
      );

      detailData.value = response.data;
    } catch (error) {
      console.error('获取凭证详情失败:', error);
      message.error('获取凭证详情失败');
    } finally {
      detailLoading.value = false;
    }
  }

  // 获取凭证文件列表
  function getVoucherFiles(voucher: Voucher) {
    const files: any[] = [];

    // 从source_info中提取文件
    if (voucher.source_info?.invoice_info?.orign_files) {
      voucher.source_info.invoice_info.orign_files.forEach((file, index) => {
        files.push({
          displayName: file.desc || `发票文件${index + 1}`,
          fullUrl: file.url,
          type: getFileType(file.url),
        });
      });
    }

    if (voucher.source_info?.bank_receipt_info?.orign_files) {
      voucher.source_info.bank_receipt_info.orign_files.forEach((file, index) => {
        files.push({
          displayName: file.desc || `银行回单${index + 1}`,
          fullUrl: file.url,
          type: getFileType(file.url),
        });
      });
    }

    return files;
  }

  // 生命周期
  onMounted(() => {
    if (companyName.value && month.value) {
      loadVouchersFromAPI();
    }

    // 添加全局键盘事件监听
    document.addEventListener('keydown', handleGlobalKeydown);
  });

  onUnmounted(() => {
    // 移除全局键盘事件监听
    document.removeEventListener('keydown', handleGlobalKeydown);
  });
</script>

<template>
  <div class="voucher-overview-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">AI凭证总览</h1>
          <div class="company-info">
            <span class="company-name">{{ companyName }}</span>
            <span class="month-info">{{ month }}</span>
          </div>
        </div>

        <div class="action-section">
          <a-button
            type="primary"
            :loading="loading"
            @click="loadVouchersFromAPI"
          >
            <template #icon>
              <SearchOutlined />
            </template>
            刷新数据
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-controls">
        <a-input
          v-model:value="searchText"
          placeholder="搜索凭证摘要、科目或金额..."
          allow-clear
          class="search-input"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input>

        <div class="filter-controls">
          <span class="filter-label">显示条数:</span>
          <a-select
            v-model:value="pageSize"
            :options="smartPageSizeOptions"
            class="page-size-select"
          />
        </div>
      </div>
    </div>

    <!-- 凭证列表 -->
    <div class="voucher-list">
      <a-spin :spinning="loading">
        <div v-if="paginatedVouchers.length === 0" class="empty-state">
          <a-empty description="暂无凭证数据" />
        </div>

        <div v-else class="voucher-cards">
          <div
            v-for="voucher in paginatedVouchers"
            :key="voucher.id"
            class="voucher-card"
            :class="{
              'generated': voucher.is_generated,
              'confirmed': voucher.confirmed,
              'selected': selectedVoucherIds.has(voucher.id)
            }"
          >
            <!-- 凭证头部 -->
            <div class="voucher-header">
              <div class="voucher-meta">
                <span class="voucher-type">{{ voucher.type }}</span>
                <span class="voucher-date">{{ voucher.record_date }}</span>
                <span class="source-type">{{ voucher.source_type }}</span>
              </div>

              <div class="voucher-actions">
                <a-button
                  size="small"
                  @click="() => showVoucherDetail(voucher)"
                >
                  查看详情
                </a-button>
              </div>
            </div>

            <!-- 凭证明细 -->
            <div class="voucher-details">
              <div
                v-for="(detail, detailIndex) in voucher.details"
                :key="detailIndex"
                class="detail-row"
              >
                <div class="detail-summary">{{ detail.summary }}</div>
                <div class="detail-account">{{ detail.account }}</div>
                <div class="detail-amounts">
                  <span class="debit-amount">
                    {{ detail.debit ? detail.debit.toFixed(2) : '-' }}
                  </span>
                  <span class="credit-amount">
                    {{ detail.credit ? detail.credit.toFixed(2) : '-' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 凭证状态 -->
            <div class="voucher-status">
              <div class="status-indicators">
                <a-tag v-if="voucher.is_generated" color="blue">AI生成</a-tag>
                <a-tag v-if="voucher.confirmed" color="green">已确认</a-tag>
                <a-tag v-if="voucher.reviewed" color="orange">已审核</a-tag>
              </div>

              <div class="executor-info">
                执行者: {{ voucher.executor }}
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="totalVouchers"
        :show-size-changer="false"
        :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        class="pagination"
      />
    </div>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="凭证详情"
      width="80%"
      :footer="null"
    >
      <SourceDataDetail
        v-if="selectedVoucher && detailData"
        :voucher="selectedVoucher"
        :detail-data="detailData"
        :loading="detailLoading"
      />
    </a-modal>

    <!-- 文件预览弹窗 -->
    <a-modal
      v-model:open="filePreviewVisible"
      :title="previewTitle"
      width="90%"
      :footer="null"
      class="file-preview-modal"
    >
      <div v-if="previewUrl" class="file-preview-content">
        <iframe
          v-if="getFileType(previewUrl) === 'pdf'"
          :src="previewUrl"
          class="pdf-preview"
        />
        <img
          v-else-if="getFileType(previewUrl) === 'image'"
          :src="previewUrl"
          :alt="previewTitle"
          class="image-preview"
        />
        <div v-else class="unsupported-file">
          <p>不支持预览此文件类型</p>
          <a-button @click="downloadFile(previewUrl, previewTitle)">
            下载文件
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
.voucher-overview-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.company-info {
  display: flex;
  gap: 16px;
  align-items: center;
}

.company-name {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.month-info {
  font-size: 14px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 12px;
  border-radius: 16px;
}

.search-section {
  background: white;
  border-radius: 8px;
  padding: 16px 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  flex: 1;
  max-width: 400px;
}

.filter-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.filter-label {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
}

.page-size-select {
  width: 120px;
}

.voucher-list {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

.voucher-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.voucher-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
  transition: all 0.2s ease;
}

.voucher-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.voucher-card.generated {
  border-color: #10b981;
  background: #f0fdf4;
}

.voucher-card.confirmed {
  border-color: #f59e0b;
  background: #fffbeb;
}

.voucher-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.voucher-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.voucher-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.voucher-type {
  font-weight: 600;
  color: #1f2937;
  background: #e5e7eb;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.voucher-date {
  font-size: 14px;
  color: #6b7280;
}

.source-type {
  font-size: 12px;
  color: #3b82f6;
  background: #dbeafe;
  padding: 2px 6px;
  border-radius: 4px;
}

.voucher-details {
  margin-bottom: 12px;
}

.detail-row {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 1fr;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
  align-items: center;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-summary {
  font-size: 14px;
  color: #374151;
}

.detail-account {
  font-size: 14px;
  color: #6b7280;
}

.detail-amounts {
  display: flex;
  gap: 12px;
}

.debit-amount,
.credit-amount {
  font-size: 14px;
  font-weight: 500;
  min-width: 80px;
  text-align: right;
}

.debit-amount {
  color: #dc2626;
}

.credit-amount {
  color: #059669;
}

.voucher-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.status-indicators {
  display: flex;
  gap: 8px;
}

.executor-info {
  font-size: 12px;
  color: #6b7280;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 24px;
}

.file-preview-modal .pdf-preview {
  width: 100%;
  height: 70vh;
  border: none;
}

.file-preview-modal .image-preview {
  width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

.unsupported-file {
  text-align: center;
  padding: 40px;
}
</style>
