<script setup lang="ts">
  import type { Voucher } from '../types';

  import { computed } from 'vue';

  interface Props {
    voucher: Voucher;
    detailData: any;
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
  });

  // 格式化金额
  function formatAmount(amount: number): string {
    return amount?.toFixed(2) || '0.00';
  }

  // 格式化日期
  function formatDate(dateStr: string): string {
    if (!dateStr) return '';
    try {
      return new Date(dateStr).toLocaleDateString('zh-CN');
    } catch {
      return dateStr;
    }
  }

  // 获取银行回单数据
  const bankReceiptData = computed(() => {
    return props.detailData?.source_info?.bank_receipt || [];
  });

  // 获取发票数据
  const invoiceData = computed(() => {
    const inputInvoice = props.detailData?.source_info?.input_invoice || [];
    const outputInvoice = props.detailData?.source_info?.output_invoice || [];
    return [...inputInvoice, ...outputInvoice];
  });

  // 获取工资单数据
  const payrollData = computed(() => {
    return props.detailData?.source_info?.payroll_info || [];
  });
</script>

<template>
  <div class="source-data-detail">
    <a-spin :spinning="loading">
      <div class="detail-sections">
        <!-- 凭证基本信息 -->
        <div class="section">
          <h3 class="section-title">凭证信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">凭证类型:</span>
              <span class="value">{{ voucher.type }}</span>
            </div>
            <div class="info-item">
              <span class="label">记录日期:</span>
              <span class="value">{{ voucher.record_date }}</span>
            </div>
            <div class="info-item">
              <span class="label">来源类型:</span>
              <span class="value">{{ voucher.source_type }}</span>
            </div>
            <div class="info-item">
              <span class="label">执行者:</span>
              <span class="value">{{ voucher.executor }}</span>
            </div>
          </div>
        </div>

        <!-- 凭证明细 -->
        <div class="section">
          <h3 class="section-title">凭证明细</h3>
          <a-table
            :columns="[
              { title: '摘要', dataIndex: 'summary', key: 'summary' },
              { title: '科目', dataIndex: 'account', key: 'account' },
              { title: '借方金额', dataIndex: 'debit', key: 'debit', align: 'right' },
              { title: '贷方金额', dataIndex: 'credit', key: 'credit', align: 'right' },
            ]"
            :data-source="voucher.details"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'debit'">
                <span class="amount debit">
                  {{ record.debit ? formatAmount(record.debit) : '-' }}
                </span>
              </template>
              <template v-else-if="column.key === 'credit'">
                <span class="amount credit">
                  {{ record.credit ? formatAmount(record.credit) : '-' }}
                </span>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 银行回单数据 -->
        <div v-if="bankReceiptData.length > 0" class="section">
          <h3 class="section-title">银行回单明细</h3>
          <a-table
            :columns="[
              { title: '交易时间', dataIndex: 'transaction_time', key: 'transaction_time' },
              { title: '账户名称', dataIndex: 'account_name', key: 'account_name' },
              { title: '对方账户', dataIndex: 'conterpary_account_name', key: 'conterpary_account_name' },
              { title: '交易类型', dataIndex: 'type', key: 'type' },
              { title: '金额', dataIndex: 'amount', key: 'amount', align: 'right' },
              { title: '摘要', dataIndex: 'summary', key: 'summary' },
            ]"
            :data-source="bankReceiptData"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'transaction_time'">
                {{ formatDate(record.transaction_time) }}
              </template>
              <template v-else-if="column.key === 'amount'">
                <span 
                  class="amount"
                  :class="{ 'debit': record.amount < 0, 'credit': record.amount > 0 }"
                >
                  {{ formatAmount(Math.abs(record.amount)) }}
                </span>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 发票数据 -->
        <div v-if="invoiceData.length > 0" class="section">
          <h3 class="section-title">发票明细</h3>
          <a-table
            :columns="[
              { title: '发票号码', dataIndex: 'invoice_number', key: 'invoice_number' },
              { title: '开票日期', dataIndex: 'issue_date', key: 'issue_date' },
              { title: '销售方', dataIndex: 'seller_name', key: 'seller_name' },
              { title: '购买方', dataIndex: 'buyer_name', key: 'buyer_name' },
              { title: '商品名称', dataIndex: 'goods_name', key: 'goods_name' },
              { title: '金额', dataIndex: 'amount', key: 'amount', align: 'right' },
              { title: '税额', dataIndex: 'tax_amount', key: 'tax_amount', align: 'right' },
              { title: '价税合计', dataIndex: 'total_amount', key: 'total_amount', align: 'right' },
            ]"
            :data-source="invoiceData"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'issue_date'">
                {{ formatDate(record.issue_date) }}
              </template>
              <template v-else-if="['amount', 'tax_amount', 'total_amount'].includes(column.key)">
                <span class="amount">
                  {{ formatAmount(record[column.key]) }}
                </span>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 工资单数据 -->
        <div v-if="payrollData.length > 0" class="section">
          <h3 class="section-title">工资单明细</h3>
          <a-table
            :columns="[
              { title: '姓名', dataIndex: 'name', key: 'name' },
              { title: '员工编号', dataIndex: 'employee_id', key: 'employee_id' },
              { title: '基本工资', dataIndex: 'base_salary', key: 'base_salary', align: 'right' },
              { title: '绩效工资', dataIndex: 'performance_salary', key: 'performance_salary', align: 'right' },
              { title: '应发工资', dataIndex: 'total_salary', key: 'total_salary', align: 'right' },
              { title: '实发工资', dataIndex: 'actual_salary', key: 'actual_salary', align: 'right' },
            ]"
            :data-source="payrollData"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="['base_salary', 'performance_salary', 'total_salary', 'actual_salary'].includes(column.key)">
                <span class="amount">
                  {{ formatAmount(record[column.key]) }}
                </span>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<style scoped>
.source-data-detail {
  padding: 16px 0;
}

.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
}

.value {
  color: #374151;
}

.amount {
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', monospace;
}

.amount.debit {
  color: #dc2626;
}

.amount.credit {
  color: #059669;
}

:deep(.ant-table) {
  background: white;
  border-radius: 6px;
}

:deep(.ant-table-thead > tr > th) {
  background: #f8fafc;
  font-weight: 600;
}
</style>
