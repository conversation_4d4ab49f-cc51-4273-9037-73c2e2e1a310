import { requestClient } from '#/api/request';

interface RecoverBookListRes {
    data: RecoverBookListItem[];
    page: number;
    pageCount: number;
    total: number;
    returnCode: string;
}
export interface RecoverBookListItem {
    name: string;
    id: string;
    accountantName: string;
    supervisorName: string;
    assistantName: string;
}

interface RecoverBookNewRes {
    returnCode: string;
    returnMsg: string;
}

// 获取回收站列表
export const getRecoverBookList = ({
    sessionUserKey,
    pageSize,
    page,
    bookName,
}: {
    bookName: string;
    page: number;
    pageSize: number;
    sessionUserKey: string;
}) =>
    requestClient.post<RecoverBookListRes>(
        `/portal/homepage/getRecoverBook.do?pageSize=${pageSize}&page=${page}&bookName=${bookName}&sessionUserKey=${sessionUserKey}`,
    );

export const recoverBookNew = ({ sessionUserKey, id }: { id: string; sessionUserKey: string }) =>
    requestClient.post<RecoverBookNewRes>(`/portal/homepage/recoverBookNew.do?id=${id}&sessionUserKey=${sessionUserKey}`);
