import { requestClient } from '#/api/request';

interface AccountBookListRes {
    data: AccountBookListItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface AccountBookListItem {
    customerId: string;
    name: string;
    id: number;
    bookId: string;
}

// 账簿列表
export const getAccountBookList = ({ sessionUserKey, standardId }: { sessionUserKey: string; standardId: string }) =>
    requestClient.post<AccountBookListRes>(`/accountBook/getAccountBook?sessionUserKey=${sessionUserKey}&standardId=${standardId}`);
