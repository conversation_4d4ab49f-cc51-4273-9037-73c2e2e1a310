import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
    /** 登录接口参数 */
    export interface LoginParams {
        password?: string;
        username?: string;
    }
}
/**
 * 登录
 */
export interface LoginData {
    code: number;
    msg: string;
    value: {
        token: string;
        userId: string;
    };
}
export async function loginApi(data: AuthApi.LoginParams) {
    // const mockdata: LoginData = {
    //     code: 1,
    //     msg: '成功',
    //     value: {
    //         userId: '7',
    //         token: 'ashdfheohgfighjzhgjkzsfngkadadfsd0gregerhherherherhrh1h56re1h231rh56erhre21hr21h23',
    //     },
    // };
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         resolve(mockdata);
    //     }, 200);
    // });
    return requestClient.post<LoginData>(`/jsjLogin.do?username=${data.username}&password=${data.password}`);
}
export type RegisterData = {
    areaCode: string;
    contactName: string;
    contactPhone: string;
    count: number; // TODO不知道啥意思 1
    extendCode: null | string;
    // imgCode: string; // 图形验证码  不要了
    isAgreement: boolean; // 是否同意用户协议
    // messageCode: string; // 手机号验证码
    orgName: '测试'; // 机构名称
    packageId: number; // TODO不知道啥意思 6
    type: number; // TODO不知道啥意思 1
};
/**
 * 注册
 */
export async function register(data: any) {
    return requestClient.post<any>(`/organizationApply/api/newRegister.do`, data);
}
/**
 * 注册前数据验证
 */
export async function registerverify(data: RegisterData) {
    return requestClient.post<any>(`/organizationApply/applyCheckFree.do`, data);
}
// 获取第一级行政地区
export async function getPrimaryUnit() {
    // /defdoc/area/root.do
    return requestClient.post<any>(`/defdoc/area/root.do`);
}
// 后续行政单位
export async function getSecondaryMarket(parentCode: string) {
    // /defdoc/area/root.do
    return requestClient.post<any>(`/defdoc/area/findByParent.do?defdocListCode=area&parentCode=${parentCode}`);
}
// 图片验证码验证接口发短信的时候需要使用
export async function verificationIsCorrect(imgcode: string) {
    return requestClient.post<any>(`/organizationApply/imgCode/validate.do?imgCode=${imgcode}`);
}

export async function sendSMS(phone: string) {
    return requestClient.post<any>(`/organizationApply/regist/send.do?phone=${phone}`);
}
/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
    return baseRequestClient.post<any>('/auth/refresh', {
        withCredentials: true,
    });
}

/**
 * 退出登录
 */
export async function logoutApi() {
    return baseRequestClient.post('/auth/logout', {
        withCredentials: true,
    });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
    return requestClient.get<string[]>('/auth/codes');
}
