import { requestClient } from '#/api/request';

/**
 * 查询客户信息列表参数
 */
export interface customerListParams {
    sessionUserKey: number | string; // 会话用户标识
    page: number; // 页码
    searchText?: string; // 搜索文本，可选
    searchId?: string; // 人员ID，可选
    deptId?: string; // 部门ID，可选
    serviceType: string; // 服务类型
    isAssign: boolean; // 是否分配
}
/**
 * 查询客户信息列表
 */
export async function getCustomerListApi(params: customerListParams) {
    // 拼接url参数
    let url = `/portal/customer/getCustomerList.do?sessionUserKey=${params.sessionUserKey}&page=${params.page}&serviceType=${params.serviceType}&isAssign=${params.isAssign}`;
    if (params.searchText) {
        url += `&searchText=${params.searchText}`;
    }
    if (params.searchId) {
        url += `&searchId=${params.searchId}`;
    }
    if (params.deptId) {
        url += `&deptId=${params.deptId}`;
    }
    return requestClient.post(url);
}

/**
 * 新增客户信息
 */
export interface AddCustomerParams {
    sessionUserKey: number | string; // 会话用户标识
    code?: string;
    name: string;
    taxType: string;
    computeType: string;
    incomeType: string;
    licenceNumber?: string;
    corporation?: string;
    corporationID?: string;
    industry?: string;
    establishDate?: string;
    contactName1?: string;
    contactTel1?: string;
    contactMail1?: string;
    contactName2?: string;
    contactTel2?: string;
    contactMail2?: string;
    contactName3?: string;
    contactTel3?: string;
    contactMail3?: string;
    area?: string;
    address?: string;
    contract: Array<[]>;
    tax: object;
    taxDetail: Array<[]>;
    id?: string;
    // 其他字段可根据实际需求补充
}
/**
 * 新增客户信息接口
 */
export async function addCustomerApi(params: AddCustomerParams) {
    return requestClient.post(`/portal/customer/save.do?sessionUserKey=${params.sessionUserKey}`, params);
}

/**
 * 查询税种参数
 */
export interface TaxTypeListParams {
    sessionUserKey: number | string;
}
/**
 * 查询税种接口
 */
export async function getTaxTypeListApi(params: TaxTypeListParams) {
    return requestClient.post(`/portal/customerTax/getTaxVarList.do?sessionUserKey=${params.sessionUserKey}`);
}

/**
 * 查询税目参数
 */
export interface TaxItemListParams {
    sessionUserKey: number | string;
    taxVarId: string;
}
/**
 * 查询税目接口
 */
export async function getTaxItemListApi(params: TaxItemListParams) {
    return requestClient.post(`/portal/customerTax/getTaxDirList.do?sessionUserKey=${params.sessionUserKey}&taxVarId=${params.taxVarId}`);
}

/**
 * 查询用户部门列表参数
 */
export interface DepartmentListParams {
    sessionUserKey: number | string;
}
/**
 * 查询用户部门列表
 */
export async function getDepartmentListApi(params: DepartmentListParams) {
    return requestClient.post(`/portal/department/search.do?sessionUserKey=${params.sessionUserKey}`);
}

/**
 * 主页面查询用户列表参数(请选择所属会计)
 */
export interface OrgUserListParams {
    sessionUserKey: number | string;
    page: number;
    orgId?: string; // 部门id，可选
    searchText?: string; // 搜索文本
}
/**
 * 主页面查询用户列表(请选择所属会计)
 */
export async function getOrgUserListApi(params: OrgUserListParams) {
    // 拼接url参数
    let url = `/reference/portal/user/getOrgUserList.do?sessionUserKey=${params.sessionUserKey}&page=${params.page}`;
    if (params.orgId) {
        url += `&orgId=${params.orgId}`;
    }
    if (params.searchText) {
        url += `&searchText=${params.searchText}`;
    }
    return requestClient.post(url);
}

/**
 * 删除客户信息参数
 */
export interface DeleteCustomerParams {
    sessionUserKey: number | string; // 会话用户标识
    id: number; // 客户ID
}
/**
 * 删除客户信息接口
 */
export async function deleteCustomerApi(params: DeleteCustomerParams) {
    console.log('Deleting customer with params:', params);
    return requestClient.post(`/portal/customer/remove.do?sessionUserKey=${params.sessionUserKey}&id=${params.id}`, undefined, {
        headers: {
            customer_id: params.id,
        },
    });
}

/**
 * 批量删除客户信息参数
 */
export interface BatchDeleteCustomerParams {
    sessionUserKey: number | string; // 会话用户标识
    ids: string[]; // 客户ID数组
}
/**
 * 批量删除客户信息接口
 */
export async function batchDeleteCustomerApi(params: BatchDeleteCustomerParams) {
    return requestClient.post(`/portal/customer/deleteAll.do?ids=${params.ids.map(Number).join(',')}&sessionUserKey=${params.sessionUserKey}`, undefined, {
        headers: {
            customer_id: params.ids.map(Number).join(','),
        },
    });
}

/**
 * 停止服务参数
 */
export interface StopServiceParams {
    sessionUserKey: number | string; // 会话用户标识
    id: number; // 客户ID
}
/**
 * 停止服务接口
 */
export async function stopServiceApi(params: StopServiceParams) {
    return requestClient.post(`/portal/customer/stopService.do?sessionUserKey=${params.sessionUserKey}&id=${params.id}`);
}

/**
 * 开始服务参数
 */
export interface StartServiceParams {
    sessionUserKey: number | string; // 会话用户标识
    id: number; // 客户ID
}
/**
 * 开始服务接口
 */
export async function startServiceApi(params: StartServiceParams) {
    return requestClient.post(`/portal/customer/beginService.do?sessionUserKey=${params.sessionUserKey}&id=${params.id}`);
}

/**
 * 查询客户基本信息参数
 */
export interface CustomerInfoParams {
    sessionUserKey: number | string; // 会话用户标识
    id: number | string; // 客户ID
}
/**
 * 查询客户基本信息接口
 */
export async function getCustomerInfoApi(params: CustomerInfoParams) {
    return requestClient.post(`/portal/customer/getCustomerInfo.do?sessionUserKey=${params.sessionUserKey}&id=${params.id}`);
}

/**
 * 查询客户合同信息参数
 */
export interface CustomerContractParams {
    sessionUserKey: number | string; // 会话用户标识
    customerId: number | string; // 客户ID
}
/**
 * 查询客户合同信息接口
 */
export async function getCustomerContractApi(params: CustomerContractParams) {
    return requestClient.post(`/portal/contract/getContractList.do?sessionUserKey=${params.sessionUserKey}&customerId=${params.customerId}`);
}

/**
 * 查询客户税务信息参数
 */
export interface CustomerTaxInfoParams {
    sessionUserKey: number | string; // 会话用户标识
    customerId: number | string; // 客户ID
}
/**
 * 查询客户税务信息接口
 */
export async function getCustomerTaxInfoApi(params: CustomerTaxInfoParams) {
    return requestClient.post(`/portal/customerTax/getTaxInfoByCustomerId.do?sessionUserKey=${params.sessionUserKey}&customerId=${params.customerId}`);
}

/**
 * 查询分配弹窗中选择人员的用户列表参数
 */
export interface UserListParams {
    sessionUserKey: number | string; // 会话用户标识
    page: number; // 页码
    // pageSize?: number;      // 每页数量（可选）
    searchText?: string; // 搜索文本（可选）
    // deptId?: string;        // 部门ID（可选）
}
/**
 * 查询分配弹窗中选择人员的用户列表接口
 */
export async function getUserListApi(params: UserListParams) {
    let url = `/reference/portal/user/getUserList.do?sessionUserKey=${params.sessionUserKey}&page=${params.page}`;
    if (params.searchText) url += `&searchText=${params.searchText}`;
    // if (params.pageSize) url += `&pageSize=${params.pageSize}`;
    // if (params.deptId) url += `&deptId=${params.deptId}`;
    return requestClient.post(url);
}

/**
 * 分配客户参数
 */
export interface AssignCustomerParams {
    sessionUserKey?: number | string; // 会话用户标识
    id: number | string; // 客户ID
    accountantId: string; // 记账会计ID
    accountantName: string;
    supervisorId: string; // 主管会计ID（可选）
    supervisorName: string;
    assistantId: string; // 助理会计ID（可选）
    assistantName: string;
    date: string;
    creatorId: string | undefined;
    creatorName: string | undefined;
}
/**
 * 分配客户接口
 */
export async function assignCustomerApi(params: AssignCustomerParams) {
    return requestClient.post(
        `/portal/customerAssign/assign.do?sessionUserKey=${params.sessionUserKey}`,
        {
            id: params.id,
            accountantId: params.accountantId,
            accountantName: params.accountantName,
            supervisorId: params.supervisorId,
            supervisorName: params.supervisorName,
            assistantId: params.assistantId,
            assistantName: params.assistantName,
            date: params.date,
            creatorId: params.creatorId,
            creatorName: params.creatorName,
        },
        {
            headers: {
                customer_id: params.id,
            },
        },
    );
}

/**
 * 批量分配客户参数
 */
export interface BatchAssignCustomerParams {
    sessionUserKey?: number | string; // 会话用户标识
    customerId: Array<number | string>; // 客户ID数组
    accountantId: string; // 记账会计ID
    accountantName: string;
    supervisorId: string; // 主管会计ID（可选）
    supervisorName: string;
    assistantId: string; // 助理会计ID（可选）
    assistantName: string;
}
/**
 * 批量分配客户接口
 */
export async function batchAssignCustomerApi(params: BatchAssignCustomerParams) {
    return requestClient.post(`/portal/customerAssign/assignMany.do?sessionUserKey=${params.sessionUserKey}`, {
        customerId: params.customerId,
        accountantId: params.accountantId,
        accountantName: params.accountantName,
        supervisorId: params.supervisorId,
        supervisorName: params.supervisorName,
        assistantId: params.assistantId,
        assistantName: params.assistantName,
    });
}

/**
 * 查询分配历史记录参数
 */
export interface AssignHistoryParams {
    sessionUserKey: number | string; // 会话用户标识
    customerId: number | string; // 客户ID
}

/**
 * 查询分配历史记录接口
 */
export async function getAssignHistoryApi(params: AssignHistoryParams) {
    return requestClient.post(`/portal/customerAssign/getAssignHistory.do?sessionUserKey=${params.sessionUserKey}&customerId=${params.customerId}`);
}

/**
 * 查询客户收款列表参数
 */
export interface ContractListParams {
    value: {
        customerKeyword?: string; // 客户名称或编号
        isShowEndContract?: boolean; // 是否已经收款完结的合同
        period: string; // 年月字符串，如 '2024-06'
        receiptSource: string; // 全部/正常/即将到期/已经欠费
    };
    sessionUserKey: number; // 会话用户标识
    page: number; // 页码
    pageSize: number; // 每页数量
}
/**
 * 查询客户收款列表
 */
export async function getContractListApi(params: ContractListParams) {
    return requestClient.post(
        `/portal/receivables/getContractList.do?page=${params.page}&pageSize=${params.pageSize}&sessionUserKey=${params.sessionUserKey}`,
        params.value,
    );
}
