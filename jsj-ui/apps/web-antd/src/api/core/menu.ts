import type { RouteRecordStringComponent } from '@vben/types';

import { requestClient } from '#/api/request';
import { uTgetlayoutUi } from '#/utils';

import { Mock_menu, Mock_menu_account } from '../mock/menu';
/**
 * 获取用户所有菜单
 */
export async function getAllMenusApi(userId: string) {
    // 假数据
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         if (uTgetlayoutUi() === 'account_book') {
    //             resolve(Mock_menu_account);
    //         } else {
    //             resolve(Mock_menu);
    //         }
    //     }, 200);
    // });
    let url: string = `/portal/resource/searchByMenu.do?type=ORG&userId=${userId}`; // 工作台的路由接口
    if (uTgetlayoutUi() === 'account_book') {
        url = `/accountant/resource/searchByMenu.do?type=ACCOUNT&userId=${userId}&sessionUserKey=${userId}`;
    }
    // /accountant/resource/searchByMenu.do?type=ACCOUNT&userId=7&sessionUserKey=7&sessionBookKey=32377
    return requestClient.post<RouteRecordStringComponent[]>(url, { checkPower: true, makePower: true, assistPower: true });
}
