import type { ApiResponse } from '#/types/api/index';

import { requestClient } from '#/api/request';

/**
 * 获取用户信息
 */
export type UserInfo = {
    id: string;
    mobile: string;
    name: string;
    org: string;
    realName: string;
    role: string;
    userHead: string;
};
export async function getUserInfoApi(userId: string) {
    // 假数据
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         resolve({
    //             data: {
    //                 realName: 'qi',
    //                 role: '财会人员，机构管理员，会计主管，军团/管理层，管理层',
    //                 userHead: 'http://jsj.cfcing.com/static/portal/index/image/user.png',
    //                 org: 'jasun521',
    //                 name: 'qitest',
    //                 mobile: '13512341234',
    //                 id: '7',
    //             },
    //             returnCode: '200',
    //         });
    //     }, 200);
    // });
    return requestClient.get<ApiResponse<UserInfo>>(`/portal/user/getCurrentUser.do?sessionUserKey=${userId}`);
}
