import { requestClient } from '#/api/request';

interface SubListItemRes {
    data: SubListItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface SubListItem {
    name: string;
    id: string;
}

export const delSubjectInfo = ({ id, sessionUserKey, sessionBookKey }: { id: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<SubListItemRes>(`/stage/subject/delete.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}&id=${id}`);
