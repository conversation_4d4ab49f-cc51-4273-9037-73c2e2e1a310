import { requestClient } from '#/api/request';

interface StopOrStartSubjectRes {
    data: StopOrStartSubjectItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface StopOrStartSubjectItem {
    balance?: number;
    balanceCheckedRemark: boolean;
    balanceDirection: string;
    balanceDirectionText: string;
    category: string;
    code: string;
    credit?: number;
    creditCheckedRemark: boolean;
    debit?: number;
    debitCheckedRemark: boolean;
    deletable: boolean;
    editable: boolean;
    enable: boolean;
    id: string;
    isAuxiliary: boolean;
    isBase: boolean;
    isCashFlow: boolean;
    isForCurrency: boolean;
    isQuantity: boolean;
    name: string;
    typeId: string;
    yearBalance?: number;
}

export const startOrStopSubject = ({
    id,
    enable,
    sessionUserKey,
    sessionBookKey,
}: {
    enable: boolean;
    id: string;
    sessionBookKey: string;
    sessionUserKey: string;
}) =>
    requestClient.post<StopOrStartSubjectRes>(
        `/stage/subject/stop.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}&id=${id}&enable=${enable}`,
    );
