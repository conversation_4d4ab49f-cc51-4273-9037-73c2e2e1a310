import { requestClient } from '#/api/request';

interface TabsInfoListRes {
    data: TabsInfoListItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface TabsInfoListItem {
    name: string;
    id: string;
}

export const getTabsInfoList = ({ sessionUserKey, sessionBookKey }: { sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<TabsInfoListRes>(`/stage/subjectType.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);
