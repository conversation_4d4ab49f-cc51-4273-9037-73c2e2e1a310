import { requestClient } from '#/api/request';

interface SubjectItemRes {
    data: SubjectItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface SubjectItem {
    balance?: number | string;
    balanceText?: number | string;
    balanceCheckedRemark: boolean;
    balanceDirection: string;
    balanceDirectionText: string;
    category: string;
    code: string;
    credit?: number | string;
    creditText?: number | string;
    creditCheckedRemark: boolean;
    debit?: number | string;
    debitText?: number | string;
    debitCheckedRemark: boolean;
    deletable: boolean;
    editable: boolean;
    enable: boolean;
    id: string;
    isAuxiliary: boolean;
    isBase: boolean;
    isCashFlow: boolean;
    isForCurrency: boolean;
    isQuantity: boolean;
    name: string;
    typeId: string;
    yearBalance?: number | string;
    yearBalanceText?: number | string;
}

export const SaveSubject = ({ params, sessionUserKey, sessionBookKey }: { params: object; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<SubjectItemRes>(`/stage/balance/save.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, params);
