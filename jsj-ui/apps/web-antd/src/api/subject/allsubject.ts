import { requestClient } from '#/api/request';

interface SubjectListRes {
    data: SubjectListItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface SubjectListItem {
    balance?: number;
    balanceCheckedRemark: boolean;
    balanceDirection: string;
    balanceDirectionText: string;
    category: string;
    code: string;
    credit?: number;
    creditCheckedRemark: boolean;
    debit?: number;
    debitCheckedRemark: boolean;
    deletable: boolean;
    editable: boolean;
    enable: boolean;
    id: string;
    isAuxiliary: boolean;
    isBase: boolean;
    isCashFlow: boolean;
    isForCurrency: boolean;
    isQuantity: boolean;
    name: string;
    typeId: string;
    yearBalance?: number;
}

export const getSubjectList = ({ sessionUserKey, sessionBookKey }: { sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<SubjectListRes>(`/stage/search.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);
