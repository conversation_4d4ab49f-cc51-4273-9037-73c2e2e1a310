import { requestClient } from '#/api/request';

interface SubjectListForMultiColumnRes {
    dataList: SubjectListForMultiColumnItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface SubjectListForMultiColumnItem {
    code: string;
    id: string;
    isQuantitySubject: boolean;
    name: string;
}

interface MultiColumnReportRes {
    dataList: MultiColumnReportItem[];
    creditColsList: Array<any>[];
    debitColsList: Array<any>[];
}
export interface MultiColumnReportItem {
    balanceDir: string;
    creditTotal: number;
    debitTotal: number;
    initialBalance: number;
    period: string;
    summary: string;
}

// 获取多栏账-下拉列表数据
export const getMultiColumnSubjectList = ({ period, searchText, param }: { param: string; period: string; searchText: string }) =>
    requestClient.post<SubjectListForMultiColumnRes>(`/report/getSubjectListForMultiColumn.do?period=${period}&searchText=${searchText}&param=${param}`);

// 获取多栏账-表格数据列表
export const getMultiColumnReport = ({ period, chartId }: { chartId: string; period: string }) =>
    requestClient.post<MultiColumnReportRes>(`/report/getMultiColumnReport.do?period=${period}&chartId=${chartId}`);
