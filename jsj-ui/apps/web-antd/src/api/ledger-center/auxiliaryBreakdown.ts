import { requestClient } from '#/api/request';

interface AuxiliaryDetailAccRes {
    dataList: AuxiliaryDetailAccItem[];
    page: number;
    records: number;
    total: number;
}
export interface AuxiliaryDetailAccItem {
    auxiliaryItemCode: string;
    auxiliaryItemName: string;
    balanceDir: string;
    debitTotal: number;
    creditTotal: number;
    initialBalance: number;
    month: string;
    period: string;
    summary: string;
    voucherDate: string;
    voucherId: string;
    voucherNo: string;
    year: string;
}

// 获取辅助核算明细账-表格列表数据
export const getAuxiliaryDetailAcc = ({
    period,
    sortType,
    chartId,
    auxiliaryType,
    auxiliaryItemId,
}: {
    auxiliaryItemId?: string;
    auxiliaryType: string;
    chartId: string;
    period: string;
    sortType: number;
}) =>
    requestClient.post<AuxiliaryDetailAccRes>(
        `/report/getAuxiliaryDetailAcc.do?period=${period}&sortType=${sortType}&chartId=${chartId}&auxiliaryType=${auxiliaryType}&auxiliaryItemId=${auxiliaryItemId}`,
    );
