import { requestClient } from '#/api/request';

interface AccBalanceRes {
    dataList: AccBalanceItem[];
    page: number;
    records: number;
    total: number;
}
export interface AccBalanceItem {
    balanceDir: string;
    chartId: string;
    initJfBalance: number;
    initDfBalance: number;
    endJfbalance: number;
    endDfbalance: number;
    currentPeriodDfBalance: number;
    currentPeriodDfQuantity: number;
    currentPeriodJfBalance: number;
    currentPeriodJfQuantity: number;
    isCalHDSY: boolean;
    isCurrSubject: boolean;
    isHavChild: number;
    isQuantity: boolean;
    showSubCode: string;
    subCode: string;
    subName: string;
    voucherNum: number;
    yearCreditTotal: number;
    yearDebitTotal: number;
}

interface AccBalanceSubjectRes {
    data: AccBalanceSubjectItem[];
    page: boolean;
    pageCount: number;
    parameter: object;
    returnCode: string;
}

export interface AccBalanceSubjectItem {
    code: string;
    id: string;
    name: string;
}

// 科目余额表
export const getAccBalanceList = ({ startPeriod, endPeriod, isShowAll, param }: { endPeriod: string; isShowAll: number; param: object; startPeriod: string }) =>
    requestClient.post<AccBalanceRes>(`/report/getAccBalance.do?startPeriod=${startPeriod}&endPeriod=${endPeriod}&isShowAll=${isShowAll}&param=${param}`);

// 获取弹窗下拉列表数据
export const getAccBalanceSubjectList = ({
    startPeriod,
    endPeriod,
    isShowAll,
    sessionUserKey,
    sessionBookKey,
}: {
    endPeriod: string;
    isShowAll: number;
    sessionBookKey: string;
    sessionUserKey: string;
    startPeriod: string;
}) =>
    requestClient.post<AccBalanceSubjectRes>(
        `/report/accBalanceSubjectList.do?startPeriod=${startPeriod}&endPeriod=${endPeriod}&isShowAll=${isShowAll}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );
