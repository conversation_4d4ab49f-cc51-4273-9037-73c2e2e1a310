import { requestClient } from '#/api/request';

interface SubjectListForAuxiliaryRes {
    data: SubjectListForAuxiliaryItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface SubjectListForAuxiliaryItem {
    code: string;
    id: string;
    isQuantitySubject: boolean;
    name: string;
}

interface AuxiliaryTypeListRes {
    data: AuxiliaryTypeListItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface AuxiliaryTypeListItem {
    text: string;
    value: string;
}

interface AuxiliaryTotalAccRes {
    dataList: AuxiliaryTotalAccItem[];
    page: number;
    records: number;
    total: number;
}
export interface AuxiliaryTotalAccItem {
    auxiliaryItemCode: string;
    auxiliaryItemId: string;
    auxiliaryItemName: string;
    balanceDir: string;
    endDebitBalance: number;
    endCreditBalance: number;
    initialDebitBalance: number;
    initialCreditBalance: number;
    period: string;
    debitTotal: number;
    creditTotal: number;
    yearCreditTotal: number;
    yearDebitTotal: number;
}

interface AuxiliaryItemListRes {
    data: AuxiliaryItemList[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface AuxiliaryItemList {
    auxItemCode: string;
    id: string;
    idx: string;
    keyword: string;
    text: string;
    value: string;
}

interface MoreAuxiliaryTotalAccRes {
    dataList: AuxiliaryItemList[];
    page: number;
    records: number;
    total: number;
}
export interface AuxiliaryItemList {
    auxiliaryItemCode: string;
    auxiliaryItemId: string;
    auxiliaryItemName: string;
    balanceDir: string;
    endDebitBalance: number;
    endCreditBalance: number;
    initialDebitBalance: number;
    initialCreditBalance: number;
    period: string;
    debitTotal: number;
    creditTotal: number;
    yearCreditTotal: number;
    yearDebitTotal: number;
}

// 获取辅助核算总账-下拉框列表数据
export const getSubjectListForAuxiliary = ({ searchText, period }: { period: string; searchText?: string }) =>
    requestClient.post<SubjectListForAuxiliaryRes>(`/report/getSubjectListForAuxiliary.do?period=${period}&searchText=${searchText}`);

// 获取辅助核算总账-辅助类型列表
export const getAuxiliaryTypeList = ({ chartId, period }: { chartId: string; period: string }) =>
    requestClient.post<AuxiliaryTypeListRes>(`/report/getAuxiliaryTypeList.do?period=${period}&chartId=${chartId}`);

// 获取辅助核算总账-表格数据（基本查询条件）
export const getAuxiliaryTotalAcc = ({
    chartId,
    auxiliaryType,
    startPeriod,
    endPeriod,
}: {
    auxiliaryType: string;
    chartId: string;
    endPeriod: string;
    startPeriod: string;
}) =>
    requestClient.post<AuxiliaryTotalAccRes>(
        `/report/getAuxiliaryTotalAcc.do?startPeriod=${startPeriod}&endPeriod=${endPeriod}&chartId=${chartId}&auxiliaryType=${auxiliaryType}`,
    );

// 获取辅助核算总账-更多查询弹窗下拉数据列表
export const getAuxiliaryItemList = ({ chartId, period, auxType }: { auxType: string; chartId: string; period: string }) =>
    requestClient.post<AuxiliaryItemListRes>(`/report/getAuxiliaryItemList.do?period=${period}&chartId=${chartId}&auxType=${auxType}`);

// 获取辅助核算总账-数据列表（更多查询条件）
export const getMoreAuxiliaryTotalAcc = ({
    startPeriod,
    endPeriod,
    auxiliaryItemIdFrom,
    auxiliaryItemIdEnd,
    balanceFlag,
    occurrenceAmountFlag,
    chartId,
    auxiliaryType,
}: {
    auxiliaryItemIdEnd: string;
    auxiliaryItemIdFrom: string;
    auxiliaryType: string;
    balanceFlag: boolean;
    chartId: string;
    endPeriod: string;
    occurrenceAmountFlag: boolean;
    startPeriod: string;
}) =>
    requestClient.post<MoreAuxiliaryTotalAccRes>(
        `/report/getMoreAuxiliaryTotalAcc.do?startPeriod=${startPeriod}&endPeriod=${endPeriod}&auxiliaryItemIdFrom=${auxiliaryItemIdFrom}&auxiliaryItemIdEnd=${auxiliaryItemIdEnd}&balanceFlag=${balanceFlag}&occurrenceAmountFlag=${occurrenceAmountFlag}&chartId=${chartId}&auxiliaryType=${auxiliaryType}`,
    );
