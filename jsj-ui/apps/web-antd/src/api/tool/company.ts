import { requestClient } from '#/api/request';

export interface UserCustomerNamesResponse {
  status: string;
  message: string;
  data: {
    data_source: string;
    saas_id: number;
    customer_names: string[];
  };
}

/**
 * 获取用户负责的客户名称列表
 * @param username 登录账号
 * @param tenant_id 租户id
 * @returns Promise<UserCustomerNamesResponse> 返回客户名称列表数据
 */
export async function getUserCustomerNames({
  username,
  tenant_id,
}: {
  username: string;
  tenant_id: string;
}): Promise<UserCustomerNamesResponse> {
  return requestClient.get<UserCustomerNamesResponse>(
    '/prod-api/autojob/api/users/get_user_info',
    {
      params: { username, tenant_id },
    }
  );
}
