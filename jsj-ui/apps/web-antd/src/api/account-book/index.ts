import type { ApiResponse } from '#/types/api/index';

import { useAccessStore } from '@vben/stores';

import { requestClient } from '#/api/request';
import { useCurrentCustomerStore } from '#/store/account-book/company';

/**
 * 获取列表信息
 */
export type CustomerItm = {
    bookId: string;
    customerId: string;
    id: string;
    name: string;
};
export type CustomerList = CustomerItm[];
// 获取公司列表
export const getCustomerList = async () => {
    const accessStore = useAccessStore();
    const useCustomer = useCurrentCustomerStore();
    return requestClient.post<ApiResponse<CustomerList>>(
        `accountBookIndex/getCustomerList.do?sessionUserKey=${accessStore.userId}&sessionBookKey=${useCustomer.customerId}`,
    );
};
// 获取发票报税页面的url
export const getOutUrl = async () => {
    const useCustomer = useCurrentCustomerStore();
    return requestClient.post<any>(`/out/url?customer_id=${useCustomer.customerId}`);
};
// 获取外币币种
export const getForeignCurrencyType = () => {
    return requestClient.post<any>(`/currency/search.do`);
};
// 获取资产类型
export const getAssetType = () => {
    return requestClient.post<any>(`/accountBook/subject/type.do`);
};

// 新增保存外币
export const addForeignSave = (data: any) => {
    return requestClient.post<any>(`/currency/save.do`, data);
};
