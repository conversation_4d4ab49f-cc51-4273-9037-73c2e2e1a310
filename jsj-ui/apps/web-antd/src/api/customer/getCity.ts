import { requestClient } from '#/api/request';

interface CityListItemRes {
    data: CityListItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}

export interface CityListItem {
    code: string;
    name: string;
    id: string;
    parentId: string;
    isLeaf: boolean;
}

export const getCityListApi = ({ defdocListCode, parentCode }: { defdocListCode: string; parentCode: string | undefined }) =>
    requestClient.post<CityListItemRes>(`/defdoc/area/findByParent.do?defdocListCode=${defdocListCode}&parentCode=${parentCode}`);
