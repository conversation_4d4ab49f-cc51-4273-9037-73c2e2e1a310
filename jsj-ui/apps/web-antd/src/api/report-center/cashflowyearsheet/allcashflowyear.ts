import { requestClient } from '#/api/request';

interface CashFlowYearRes {
    dataList: CashFlowYearItem[];
    page: number;
    records: number;
    total: number;
}
export interface CashFlowYearItem {
    proName: string;
    rowNum?: number;
    toQryMonthTotal?: number;
    autoFormula?: string;
    curYearTotal?: string;
    preYearTotal?: string;
    proCode: string;
    qryMonthTotal?: number;
    yearTotal: Array<null | string>;
}

// 现金流量年表
export const getCashFlowYearListApi = ({ year }: { year: string }) => requestClient.post<CashFlowYearRes>(`/report/getCashYearReport.do?year=${year}`);
