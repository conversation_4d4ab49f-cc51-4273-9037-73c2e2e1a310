import { requestClient } from '#/api/request';

interface ProfitYearRes {
    dataList: ProfitYearItem[];
}
export interface ProfitYearItem {
    expanded: boolean;
    id: number;
    isLeaf: boolean;
    level: number;
    loaded: boolean;
    parent: string;
    proName: string;
    rowNum: number;
    toLastQryMonthTotal: number;
    toQryMonthTotal: number;
    children?: ProfitYearItem[];
}

// 利润年表
export const getProfitYearListApi = ({ year }: { year: string }) => requestClient.post<ProfitYearRes>(`/report/getProfitsYearReport.do?year=${year}`);
