import { requestClient } from '#/api/request';

interface BalanceRes {
    dataList: BalanceItem[];
    isFlat: boolean;
    profit?: string;
    initialBalance?: string;
    yearInitBalance?: string;
    subjectDirectionDiffrentDtos?: SubjectDiffItem[];
}
export interface BalanceItem {
    assetsEndBalance: number;
    assetsMode: string;
    assetsName: string;
    assetsRowNum: string;
    assetsTitle: string;
    assetsYearInitBalance: number;
    defaultAssetsFormula: boolean;
    defaultFzqyFormula: boolean;
    expanded: boolean;
    fzqyEndBalance: number;
    fzqyMode: string;
    fzqyName: string;
    fzqyRowNum: string;
    fzqyTitle: string;
    fzqyYearInitBalance: number;
    havAuxNum: number;
    id: number;
    isHavChild: number;
    isLeaf: true;
    level: number;
    loaded: boolean;
    parent: string;
}

export interface SubjectDiffItem {
    bookDirectionType: string;
    subjectCode: string;
    subjectName: string;
    sysDirectionType: string;
}

interface YearMonthRes {
    accountDate: string;
    inputDate: string;
}

// 资产负债列表
export const getBalanceListApi = ({ yearMonth, isClassify }: { isClassify: string; yearMonth: string }) =>
    requestClient.post<BalanceRes>(`/report/getAssetsReport.do?isClassify=${isClassify}&yearMonth=${yearMonth}`);

// 查询年份月份
export const getYearMonthApi = () => requestClient.post<YearMonthRes>(`/report/accountdate.do`);
