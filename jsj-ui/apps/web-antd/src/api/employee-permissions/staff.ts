import { requestClient } from '#/api/request';

interface DepartmentDataRes {
    data: DepartmentDataItem[];
}
export interface DepartmentDataItem {
    name: string;
    id: string;
    type: string;
    isLeaf: boolean;
    fatherId?: string;
    orgId?: string;
    parentId?: string;
    editing?: boolean;
    deptId?: string;
    childs: DepartmentDataItem[];
}
export interface EditPayload {
    name?: string;
    fatherId?: string;
    orgId?: string;
    id?: string;
    dept?: string;
    isPortalAccountant?: boolean;
    realName?: string;
    roleIds?: string[];
    type?: string[];
    externalUserId?: string;
    email?: string;
    mobile?: string;
}
interface EditPortalDepartmentRes {
    name: string;
    id: string;
    type: string;
    isLeaf: boolean;
    orgId: string;
}
interface DelPortalDepartmentRes {
    returnCode: string;
}

interface UserSearchDataRes {
    data: UserSearchDataItem[];
}
export interface UserSearchDataItem {
    realName: string;
    noChange: boolean;
    role: string;
    isOrgUser: boolean;
    enable: boolean;
    isOrg: boolean;
    name: string;
    mobile: string;
    id: string;
    dept: string;
}

interface RoleListForBindRes {
    data: RoleListForBindItem[];
    returnCode: string;
}
export interface RoleListForBindItem {
    code: string;
    isBind: boolean;
    name: string;
    id: string;
}

interface GetPortalUserInfoRes {
    data: PortalUserInfoData;
    returnCode: string;
}
export interface PortalUserInfoData {
    isInvoiceManager: boolean;
    isOrgManager: boolean;
    mobile: string;
    isPortalAccountant: boolean;
    isSaler: boolean;
    realName: string;
    password: string;
    isAuditor: boolean;
    isBoss: boolean;
    name: string;
    id: string;
}

// 组织管理列表
export const getDepartmentData = (sessionUserKey: string) =>
    requestClient.post<DepartmentDataRes>(`/portal/department/search.do?sessionUserKey=${sessionUserKey}`);

// 组织管理新增
export const editPortalDepartment = ({ sessionUserKey, params }: { params: EditPayload; sessionUserKey: string }) =>
    requestClient.post<EditPortalDepartmentRes>(`/portal/department/save.do?sessionUserKey=${sessionUserKey}`, params);

// 组织管理删除
export const delPortalDepartment = ({ id, sessionUserKey }: { id?: string; sessionUserKey: string }) =>
    requestClient.post<DelPortalDepartmentRes>(`/portal/department/del.do?id=${id}&sessionUserKey=${sessionUserKey}`);

// 角色
export const getRoleListForBind = (sessionUserKey: string) =>
    requestClient.post<RoleListForBindRes>(`/portal/user/getRoleListForBind.do?sessionUserKey=${sessionUserKey}`);

// 人员管理新增
export const saveUserOrg = ({ sessionUserKey, params }: { params: EditPayload; sessionUserKey: string }) =>
    requestClient.post<EditPortalDepartmentRes>(`userManage/api/user/addDept.do?sessionUserKey=${sessionUserKey}`, params);
export const saveUserDept = ({ sessionUserKey, params }: { params: EditPayload; sessionUserKey: string }) =>
    requestClient.post<EditPortalDepartmentRes>(`/portal/user/deptSave.do?sessionUserKey=${sessionUserKey}`, params);

// 人员管理编辑内容反显
export const getPortalUserInfo = ({ id, sessionUserKey }: { id: string; sessionUserKey: string }) =>
    requestClient.post<GetPortalUserInfoRes>(`/portal/user/info.do?id=${id}&sessionUserKey=${sessionUserKey}`);

// 启用停用
export const stopAllUser = ({ id, enable, sessionUserKey }: { enable: boolean; id: string; sessionUserKey: string }) =>
    requestClient.post<DelPortalDepartmentRes>(`/portal/user/stopAll.do?id=${id}&enable=${enable}&sessionUserKey=${sessionUserKey}`);

// 删除
export const delUser = ({ id, sessionUserKey }: { id?: string; sessionUserKey: string }) =>
    requestClient.post<DelPortalDepartmentRes>(`/portal/user/del.do?id=${id}&sessionUserKey=${sessionUserKey}`);

// 删除All
export const delAllUser = ({ id, sessionUserKey }: { id: string; sessionUserKey: string }) =>
    requestClient.post<DelPortalDepartmentRes>(`/portal/user/delAll.do?id=${id}&sessionUserKey=${sessionUserKey}`);

// 更换部门
export const changeDeptUser = ({
    id,
    selectId,
    type,
    orgId,
    sessionUserKey,
}: {
    id: string;
    orgId?: string;
    selectId: string;
    sessionUserKey: string;
    type: string;
}) =>
    requestClient.post<DelPortalDepartmentRes>(
        `/portal/user/changeDept.do?id=${id}&selectId=${selectId}&type=${type}&orgId=${orgId}&sessionUserKey=${sessionUserKey}`,
    );

// 人员管理列表
export const getUserSearchData = ({
    sessionUserKey,
    searchText,
    id,
    include,
    page,
    idType,
}: {
    id: string;
    idType: string;
    include: boolean;
    page: number;
    searchText: string;
    sessionUserKey: string;
}) =>
    requestClient.post<UserSearchDataRes>(
        `/portal/user/searchByOrg.do?page=${page}&searchText=${searchText}&${idType}=${id}&include=${include}&sessionUserKey=${sessionUserKey}`,
    );
