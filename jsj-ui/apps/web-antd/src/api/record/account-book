//保存凭证参数
//url /voucher/save.do?sessionUserKey=7&sessionBookKey=32250
//提交的数据
const todata = {
    voucherNo: 4, //凭证编号
    debit: '340.00', // 借方总金额
    credit: '340.00', //
    detail: [
        {
            summary: '提现',
            subjectId: '9222721081396944357',
            debit: '10',
        },
        {
            summary: '存现',
            subjectId: '9222721081396944358',
            debit: '180',
        },
        {
            summary: '提现',
            subjectId: '9222721081396944357',
            debit: '34',
        },
        {
            summary: '提现',
            subjectId: '9222721081396944357',
            debit: '49',
        },
        {
            summary: '存现',
            subjectId: '9222721081396944357',
            debit: '67',
        },
        {
            summary: '存现',
            subjectId: '9222721081396944357',
            credit: '340',
        },
    ],
    dateTime: 1733011200000,
    voucherNumber: 4,
    voucherWord: '记',
    voucherType: 'NORMAL',
    attachmentCount: 0,
    insertMode: false,
    insertWord: null,
    insertNumber: null,
    fileList: [],
};
//返回的数据
const res = {
    data: {
        date: '2024-12-01',
        code: '004',
        voucherType: 'NORMAL',
        type: '普通凭证',
        totalAmount: '叁佰肆拾元整',
        debitText: '340.00',
        voucherWord: '记',
        voucherNumber: 4,
        attachmentCount: 0,
        details: [],
        id: '9222401976571953270',
        creditText: '340.00',
        state: 'NONE',
        debit: 340,
        credit: 340,
    },
    returnCode: '200',
};
function transformStr(money) {
  if(money && !isNaN(parseFloat(money)) && isFinite(money)) {
   // 默认值设置为“零元整”，如果输入为空或非法，则返回这个默认值
   let cnMoney = '零元整';
   // 用于构建输出字符串的变量
   let strOutput = '';
   // 中文单位数组，从高到低排列
   let strUnit = '仟佰拾亿仟佰拾万仟佰拾元角分';
   // 定义数字位数字符串
   let numCapitalLetters = '零壹贰叁肆伍陆柒捌玖';
   // 将传入的金额字符串后添加两个零，确保小数部分存在
   money += '00';
   // 查找小数点的位置
   let intPos = money.indexOf('.');
   if(intPos >= 0) {
    // 如果存在小数点，则移除小数点，并保留前两位作为小数部分
    money = money.substring(0, intPos) + money.substr(intPos + 1, 2);
   }
   // 长度限制
   if((strUnit.length - money.length) < 0) return '长度超出限制，最大支持千亿位';
   // 根据money的长度裁剪strUnit，以匹配money中每个数字对应的中文单位
   strUnit = strUnit.substr(strUnit.length - money.length);
   // 遍历money中的每一个字符
   for(var i = 0; i < money.length; i++) {
    // 对应数字转换为中文大写，并加上相应的中文单位
    strOutput += numCapitalLetters.substr(money.substr(i, 1), 1) + strUnit.substr(i, 1);
   }
   // 使用正则表达式处理中文大写金额字符串，进行格式化
   cnMoney = strOutput
   .replace(/零角零分$/, '整') // 如果最后是“零角零分”，替换为“整”
   .replace(/零[仟佰拾]/g, '零') // 移除多余的零（如“零仟”、“零佰”等）
   .replace(/零{2,}/g, '零') // 连续多个零只保留一个
   .replace(/零([亿|万])/g, '$1') // 去掉亿、万前面的零
   .replace(/零+元/, '元') // 去掉元前面的所有零
   .replace(/亿零{0,3}万/, '亿') // 如果亿后面有零和万，只保留亿
   .replace(/^元/, '零元'); // 如果最开始就是元，添加“零”
   
   // 返回最终处理后的中文大写金额字符串
   return cnMoney;
  }
  else {
   return '非法参数或参数不存在';
  }
}
