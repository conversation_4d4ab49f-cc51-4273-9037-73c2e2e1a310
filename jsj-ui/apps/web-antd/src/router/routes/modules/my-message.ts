import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      // badgeType: 'dot',
      icon: 'icon-park-solid:permissions',
      order: 10,
      hideInMenu: true,
      title: '我的消息',
      authority: uTgetManagement(['message']),
    },
    name: 'my-message',
    path: '/my-message',
    component: () => import('#/views/default/index.vue'),
  },
];

export default routes;
