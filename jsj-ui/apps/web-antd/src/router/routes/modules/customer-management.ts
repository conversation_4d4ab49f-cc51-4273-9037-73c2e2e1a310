import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'lucide:align-end-vertical',
            order: 3,
            title: '客户管理',
            authority: uTgetManagement(['customer']),
        },
        name: 'customer-management',
        path: '/customer-management',
        children: [
            {
                name: 'customer-management-information',
                path: '/customer-management/information',
                component: () => import('#/views/customer/customerInfo.vue'),
                meta: {
                    icon: 'lucide:align-end-vertical',
                    title: '客户信息',
                    authority: uTgetManagement(['cusertomerInfo']),
                },
            },
            {
                name: 'customer-management-collection',
                path: '/customer-management/collection',
                component: () => import('#/views/customer/customerCollection.vue'),
                meta: {
                    icon: 'mdi:github',
                    title: '客户收款',
                    authority: uTgetManagement(['receivables']),
                },
            },
        ],
    },
];

export default routes;
