import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ant-design:container-filled',
      order: 5,
      title: '服务管理',
      authority: uTgetManagement(['serviceManage']),
    },
    name: 'service-management',
    path: '/service-management',
    children: [
      {
        name: 'service-management-promotion',
        path: '/service-management/promotion',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'lucide:book-open-text',
          title: '推广信息',
          authority: uTgetManagement(['orgExtensionAnalysis']),
        },
      },
      {
        name: 'service-management-progress',
        path: '/service-management/progress',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '服务进度设置',
          authority: uTgetManagement(['serSetting']),
        },
      },
      {
        name: 'service-management-confirm',
        path: '/service-management/confirm',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '服务进度确认',
          authority: uTgetManagement(['serAffirm']),
        },
      },
      {
        name: 'service-management-log',
        path: '/service-management/log',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '用户日志',
          authority: uTgetManagement(['userLog']),
        },
      },
      {
        name: 'service-management-summary-table',
        path: '/service-management/summary-table',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '工作量统计汇总表',
          authority: uTgetManagement(['portalCollect']),
        },
      },
      {
        name: 'service-management-detail-table',
        path: '/service-management/detail-table',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '工作量统计明细表',
          authority: uTgetManagement(['portalDetail']),
        },
      },
      {
        name: 'service-management-backup',
        path: '/service-management/backup',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '备份管理',
          authority: uTgetManagement(['backList']),
        },
      },
      {
        name: 'service-management-chronological',
        path: '/service-management/chronological',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '序时账管理',
          authority: uTgetManagement(['xszList']),
        },
      },
      {
        name: 'service-management-invoice',
        path: '/service-management/invoice',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '开票管理',
          authority: uTgetManagement(['invoiceManage']),
        },
      },
      {
        name: 'service-management-account-period',
        path: '/service-management/account-period',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '账期汇总',
          authority: uTgetManagement(['billStatistic']),
        },
      },
    ],
  },
];

export default routes;
