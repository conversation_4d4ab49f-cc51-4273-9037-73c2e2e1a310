import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 2,
            title: '职员权限',
            authority: uTgetManagement(['userPermission']),
        },
        name: 'employee-permissions',
        path: '/employee-permissions',
        children: [
            {
                name: 'employee-permissions-staff',
                path: '/employee-permissions/staff',
                component: () => import('#/views/employee-permissions/staff/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '组织职员',
                    authority: uTgetManagement(['deptUser']),
                },
            },
            {
                name: 'employee-permissions-role',
                path: '/employee-permissions/role',
                component: () => import('#/views/employee-permissions/role/index.vue'),
                meta: {
                    icon: 'ep:bottom',
                    title: '角色权限',
                    authority: uTgetManagement(['rolePermission']),
                },
            },
        ],
    },
];

export default routes;
