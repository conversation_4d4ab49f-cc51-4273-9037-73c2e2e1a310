import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 9,
            // noBasicLayout:true,//不使用基础布局
            title: '报税',
            outUrlType: 'tax', // 使用的外部页面这个是报税页面
        },
        name: 'account-book-tax',
        path: '/account-book/tax',
        component: () => import('#/views/iframe/index.vue'),
    },
];

export default routes;
