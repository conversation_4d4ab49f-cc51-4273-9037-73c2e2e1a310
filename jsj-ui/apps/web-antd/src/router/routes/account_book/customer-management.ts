import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            // noBasicLayout: true,
            icon: 'lucide:align-end-vertical',
            order: 1,
            title: '主页',
            authority: uTgetManagement(['homepage']),
        },
        name: 'account-book-homepage',
        path: '/account-book/homepage',
        component: () => import('#/views/account-book/homepage/index.vue'),
    },
];

export default routes;
