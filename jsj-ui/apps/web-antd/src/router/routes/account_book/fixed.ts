import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 6,
            // noBasicLayout:true,//不使用基础布局
            title: '固定资产',
        },
        name: 'account-book-fixed',
        path: '/account-book/fixed',
        children: [
            {
                name: 'account-book-fixed-category',
                path: '/account-book/fixed/category',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '类别及增减方式',
                    authority: uTgetManagement(['assetsReport']),
                },
            },
            {
                name: 'account-book-fixed-management',
                path: '/account-book/fixed/management',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '资产管理',
                    authority: uTgetManagement(['profitsReport']),
                },
            },
            {
                name: 'account-book-fixed-provision',
                path: '/account-book/fixed/provision',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '计提折旧',
                    authority: uTgetManagement(['profitsQuarterReport']),
                },
            },
            {
                name: 'account-book-fixed-impairment',
                path: '/account-book/fixed/impairment',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '计提减值准备',
                    authority: uTgetManagement(['profitsYearReport']),
                },
            },
            {
                name: 'account-book-fixed-liquidation',
                path: '/account-book/fixed/liquidation',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '资产清理',
                    authority: uTgetManagement(['cashFlowReport']),
                },
            },
            {
                name: 'account-book-fixed-report',
                path: '/account-book/fixed/report',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '资产报表',
                    authority: uTgetManagement(['cashFlowYearReport']),
                },
            },
        ],
    },
];

export default routes;
