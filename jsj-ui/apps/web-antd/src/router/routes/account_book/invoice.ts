import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 10,
            // noBasicLayout:true,//不使用基础布局
            title: '发票',
            outUrlType: 'invoice', // 使用的外部页面这个是报税页面
        },
        name: 'account-book-invoice',
        path: '/account-book/invoice',
        component: () => import('#/views/iframe/index.vue'),
    },
];

export default routes;
