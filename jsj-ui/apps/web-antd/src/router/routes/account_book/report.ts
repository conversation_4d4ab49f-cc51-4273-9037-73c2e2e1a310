import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 6,
            // noBasicLayout:true,//不使用基础布局
            title: '报表中心',
            authority: uTgetManagement(['sumReport']),
        },
        name: 'account-book-report',
        path: '/account-book/report',
        children: [
            {
                name: 'account-book-report-balance',
                path: '/account-book/report/balance',
                component: () => import('#/views/reportCenter/balanceSheet/allbalance/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '资产负债表',
                    authority: uTgetManagement(['assetsReport']),
                },
            },
            {
                name: 'account-book-report-income',
                path: '/account-book/report/income',
                component: () => import('#/views/reportCenter/profitSheet/allprofit/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '利润表',
                    authority: uTgetManagement(['profitsReport']),
                },
            },
            {
                name: 'account-book-report-profit',
                path: '/account-book/report/profit',
                component: () => import('#/views/reportCenter/profitSeasonSheet/allprofitseason/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '利润表季报',
                    authority: uTgetManagement(['profitsQuarterReport']),
                },
            },
            {
                name: 'account-book-report-annual',
                path: '/account-book/report/annual',
                component: () => import('#/views/reportCenter/profitYearSheet/allprofityear/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '利润表年报',
                    authority: uTgetManagement(['profitsYearReport']),
                },
            },
            {
                name: 'account-book-report-statement',
                path: '/account-book/report/statement',
                component: () => import('#/views/reportCenter/cashFlowSheet/allcashflow/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '现金流量表',
                    authority: uTgetManagement(['cashFlowReport']),
                },
            },
            {
                name: 'account-book-report-cashquarterly',
                path: '/account-book/report/cashquarterly',
                component: () => import('#/views/reportCenter/cashFlowSeasonSheet/allcashflowseason/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '现金流量表季报',
                    authority: uTgetManagement(['cashQuarterReport']),
                },
            },
            {
                name: 'account-book-report-cashannual',
                path: '/account-book/report/cashannual',
                component: () => import('#/views/reportCenter/cashFlowYearSheet/allcashflowyear/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '现金流量表年报',
                    authority: uTgetManagement(['cashFlowYearReport']),
                },
            },
        ],
    },
];

export default routes;
