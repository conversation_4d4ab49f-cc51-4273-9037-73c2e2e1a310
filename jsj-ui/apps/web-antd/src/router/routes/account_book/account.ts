import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 4,
            // noBasicLayout:true,//不使用基础布局
            title: '账簿 ',
            authority: uTgetManagement(['accountReport']),
        },
        name: 'account-book-account',
        path: '/account-book/account',
        children: [
            {
                name: 'account-book-account-balance',
                path: '/account-book/account/balance',
                component: () => import('#/views/ledgerCenter/accountBalances/mainEntrance/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '科目余额表',
                    authority: uTgetManagement(['accBalance']),
                },
            },
            {
                name: 'account-book-account-general',
                path: '/account-book/account/general',
                component: () => import('#/views/ledgerCenter/ledger/allLedger/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '总账',
                    authority: uTgetManagement(['totalAcc']),
                },
            },
            {
                name: 'account-book-account-sub',
                path: '/account-book/account/sub',
                component: () => import('#/views/ledgerCenter/breakdown/mainbreakdown/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '明细账',
                    authority: uTgetManagement(['detailAcc']),
                },
            },
            {
                name: 'account-book-account-auxiliary',
                path: '/account-book/account/auxiliary',
                component: () => import('#/views/ledgerCenter/ancillaryAccountLedger/auxiliaryLedger/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '辅助核算总帐',
                    authority: uTgetManagement(['auxiliaryTotalAcc']),
                },
            },
            {
                name: 'account-book-account-detailed',
                path: '/account-book/account/detailed',
                component: () => import('#/views/ledgerCenter/ancillaryAccountBreakdown/auxiliaryBreakdown/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '辅助核算明细账',
                    authority: uTgetManagement(['auxiliaryDetailAcc']),
                },
            },
            {
                name: 'account-book-account-multi',
                path: '/account-book/account/multi',
                component: () => import('#/views/ledgerCenter/multiColumnAccount/multiColumn/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '多栏账',
                    authority: uTgetManagement(['multiColumn']),
                },
            },
        ],
    },
];

export default routes;
