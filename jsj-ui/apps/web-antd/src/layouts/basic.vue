<script lang="ts" setup>
import type { CustomerItm, CustomerList } from '#/api/account-book/index';

import { computed, h, onMounted, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationLoginExpiredModal, useVbenDrawer } from '@vben/common-ui';
import { useRefresh, useWatermark } from '@vben/hooks';
import { BookOpenText, GiteeIcon, UserOutlined } from '@vben/icons';
import { BasicLayout, LockScreen, Notification, UserDropdown } from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';

import { LoadingOutlined } from '@ant-design/icons-vue';

import logoText from '#/common/img/logo-text.png';
import useCustomerList from '#/hooks/account-book/useCustomerList';
import { useAuthStore } from '#/store';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import { uTgetlayoutUi } from '#/utils';
import LoginForm from '#/views/_core/authentication/login.vue';
// 修改密码
import ChangePassword from '#/views/_core/profile/change-password/index.vue';
// 机构信息
import InstitutionalInformation from '#/views/_core/profile/institutional-information/index.vue';
// 个人信息
import PersonalInformation from '#/views/_core/profile/personal-information/index.vue';

const router = useRouter();
const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const use_customer_list = useCustomerList();
const { destroyWatermark, updateWatermark } = useWatermark();
const [Password, PasswordApi] = useVbenDrawer({
    // 连接抽离的组件
    connectedComponent: ChangePassword,
});
const [Institutional, InstitutionalApi] = useVbenDrawer({
    // 连接抽离的组件
    connectedComponent: InstitutionalInformation,
});
const [PersonalIn, PersonalInApi] = useVbenDrawer({
    // 连接抽离的组件
    connectedComponent: PersonalInformation,
});

const state = reactive<{
    company: string;
    list: CustomerList;
}>({
    company: '',
    list: [],
});
const { refresh } = useRefresh();
onMounted(async () => {
    const useCustomer = useCurrentCustomerStore();
    state.company = useCustomer.name;
});
// 公司切换
const companyChage = (customerId: string, data: CustomerItm) => {
    console.log(555, customerId, data);
    const useCustomer = useCurrentCustomerStore();
    useCustomer.setCustomerData(data);
    // 需要刷新下
    // router.go(0);
    refresh();
};
// 公司切换下拉框展示
const companyChageShow = () => {
    if (use_customer_list.selectdata.value.length === 0) {
        use_customer_list.fetchData();
    }
};
// 返回工作台
const returnToWorkbench = () => {
    // 这里不能用 router.push 方法 会报错，原因是 这个本身是两套路由；
    // router.push 是局部刷新 不会获取从新获取路由
    location.href = '/workbenches/alluser';
    // router.push('/workbenches/alluser');
};
const menus = computed(() => {
    const defaultMenus = [
        {
            handler: () => {
                PersonalInApi.open();
            },
            icon: BookOpenText,
            text: '个人信息',
        },
        {
            handler: () => {
                InstitutionalApi.open();
            },
            icon: UserOutlined,
            text: '机构信息',
        },
        {
            handler: () => {
                PasswordApi.open();
            },
            icon: () => h(GiteeIcon, { class: 'text-red-800' }),
            text: '修改密码',
        },
    ];
    /**
     * 在账簿页面中不显示机构信息
     */
    if (uTgetlayoutUi() === 'account_book') {
        defaultMenus.splice(1, 1);
    }
    return defaultMenus;
});

const avatar = computed(() => {
    return userStore.userInfo?.avatar ?? preferences.app.defaultAvatar;
});

async function handleLogout() {
    await authStore.logout(false);
}

watch(
    () => preferences.app.watermark,
    async (enable) => {
        if (enable) {
            await updateWatermark({
                content: `${userStore.userInfo?.username} - ${userStore.userInfo?.realName}`,
            });
        } else {
            destroyWatermark();
        }
    },
    {
        immediate: true,
    },
);
const messageClick = () => {
    router.push('/my-message');
};
</script>

<template>
    <BasicLayout @clear-preferences-and-logout="handleLogout">
        <template #logo-text>
            <div style="text-align: center">
                <img
                    style="width: auto; height: 30px; margin-top: 5px; vertical-align: top"
                    :src="logoText"
                />
            </div>
        </template>
        <template
            #account_btn
            v-if="uTgetlayoutUi() === 'account_book'"
        >
            <a-select
                :field-names="{
                    label: 'name',
                    value: 'customerId',
                }"
                @change="companyChage"
                v-model:value="state.company"
                show-search
                style="width: 200px"
                placeholder="Select a person"
                :options="use_customer_list.selectdata"
                @dropdown-visible-change="companyChageShow"
            >
                <template
                    v-if="use_customer_list.selectdata.value.length === 0"
                    #dropdownRender
                >
                    <div class="text-center"><LoadingOutlined /></div>
                </template>
            </a-select>
            <a-button
                class="ml-2"
                type="primary"
                size="middle"
                @click="returnToWorkbench"
            >
                <template #icon="">
                    <DownloadOutlined />
                </template>
                返回工作台
            </a-button>
        </template>
        <template #user-dropdown>
            <UserDropdown
                :avatar
                :menus
                :text="userStore.userInfo?.realName"
                description="<EMAIL>"
                tag-text="Pro"
                @logout="handleLogout"
            />
        </template>
        <template #notification>
            <Notification
                :dot="true"
                :onclick="messageClick"
            />
        </template>
        <template #extra>
            <AuthenticationLoginExpiredModal
                v-model:open="accessStore.loginExpired"
                :avatar
            >
                <LoginForm />
            </AuthenticationLoginExpiredModal>
        </template>
        <template #lock-screen>
            <LockScreen
                :avatar
                @to-login="handleLogout"
            />
        </template>
    </BasicLayout>
    <Password />
    <Institutional />
    <PersonalIn
        onchange=""
        on-before-close="false"
    />
</template>
