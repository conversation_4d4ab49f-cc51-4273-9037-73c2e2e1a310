import type { CustomerItm } from '#/api/account-book/index';

import { defineStore } from 'pinia';
/**
 * 公司信息 进入账簿的时候要用到
 */
export const useCurrentCustomerStore = defineStore('core-current-customer', {
    actions: {
        setCustomerData(data: CustomerItm) {
            this.customerId = data.customerId;
            this.bookId = data.bookId;
            this.id = data.id;
            this.name = data.name;
        },
    },
    persist: {
        pick: ['customerId', 'bookId', 'id', 'name'],
    },
    state: (): CustomerItm => ({
        customerId: '',
        bookId: '',
        id: '',
        name: '',
    }),
});
