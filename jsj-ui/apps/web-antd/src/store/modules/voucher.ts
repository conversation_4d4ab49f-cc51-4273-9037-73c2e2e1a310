import { ref } from 'vue';

import { defineStore } from 'pinia';

export interface VoucherDetail {
  summary: string;
  account: string;
  debit: number;
  credit: number;
  account_code: string;
  parent_account_info: null | string;
  auxiliary_accounting_list: Array<{
    id: number;
    name: string;
    type: string;
  }>;
  confidence: number;
}

export interface VoucherData {
  request_id: string;
  company_info: {
    business_scope: string;
    company_name: string;
    company_type: string;
    customer_id: string;
    tax_id: string;
  };
  voucher_type: string;
  vouchers: Array<{
    details: VoucherDetail[];
    executor: string;
    record_date: string;
    source: string[];
    type: string;
  }>;
}

// 银行回单生成凭证的数据结构
export interface GeneratedVoucherDetail {
  account_code: string;
  account_name: string;
  summary: string;
  debit: number;
  credit: number;
}

export interface GeneratedVoucher {
  voucher_number: string;
  voucher_type: string;
  date: string;
  description: string;
  currency: string;
  details: GeneratedVoucherDetail[];
  source_data: {
    account_number: string;
    conversion_method: string;
    original_amount: number;
    transaction_id: string;
  };
}

export interface VoucherGenerationData {
  conversion_summary: {
    balance_check: boolean;
    failed_conversions: number;
    success_rate: number;
    successful_conversions: number;
    total_credit_amount: number;
    total_debit_amount: number;
    total_records_processed: number;
    total_vouchers_generated: number;
  };
  generated_vouchers: GeneratedVoucher[];
  validation_results: {
    balance_issues: any[];
    invalid_vouchers: number;
    total_vouchers: number;
    valid_vouchers: number;
    validation_errors: any[];
  };
  conversion_errors: any[];
  metadata: {
    conversion_time: number;
    total_processing_time: string;
    voucher_settings: {
      auto_balance: boolean;
      default_currency: string;
      rounding_precision: number;
      voucher_prefix: string;
    };
  };
}

export const useVoucherStore = defineStore('voucher', () => {
  const currentVoucher = ref<null | VoucherData>(null);
  // 存储银行回单生成的凭证数据
  const generatedVoucherData = ref<null | VoucherGenerationData>(null);
  // 存储原始银行回单数据
  const bankReceiptData = ref<any | null>(null);

  function $reset() {
    currentVoucher.value = null;
    generatedVoucherData.value = null;
    bankReceiptData.value = null;
  }

  function setVoucherData(data: VoucherData) {
    currentVoucher.value = data;
  }

  function clearVoucherData() {
    currentVoucher.value = null;
  }

  // 设置银行回单生成的凭证数据
  function setGeneratedVoucherData(data: VoucherGenerationData) {
    generatedVoucherData.value = data;
  }

  // 清除银行回单生成的凭证数据
  function clearGeneratedVoucherData() {
    generatedVoucherData.value = null;
  }

  // 设置银行回单数据
  function setBankReceiptData(data: any) {
    bankReceiptData.value = data;
  }

  // 清除银行回单数据
  function clearBankReceiptData() {
    bankReceiptData.value = null;
  }

  return {
    currentVoucher,
    generatedVoucherData,
    bankReceiptData,
    setVoucherData,
    clearVoucherData,
    setGeneratedVoucherData,
    clearGeneratedVoucherData,
    setBankReceiptData,
    clearBankReceiptData,
    $reset,
  };
});
