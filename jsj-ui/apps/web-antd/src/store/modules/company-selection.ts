import { ref } from 'vue';

import { defineStore } from 'pinia';

export const useCompanySelectionStore = defineStore(
  'company-selection',
  () => {
    // 存储选中的公司名称
    const selectedCompany = ref<string>(''); // 默认为空，等待从API获取后设置

    // 设置选中的公司
    const setSelectedCompany = (companyName: string) => {
      selectedCompany.value = companyName;
    };

    // 获取选中的公司
    const getSelectedCompany = () => {
      return selectedCompany.value;
    };

    return {
      selectedCompany,
      setSelectedCompany,
      getSelectedCompany,
    };
  },
  {
    persist: true, // 持久化存储
  },
);
