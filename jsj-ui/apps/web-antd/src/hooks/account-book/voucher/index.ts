/**
 * 摘要选择逻辑
 */
import type { AuxiliaryItm, SummaryItmData } from '#/api/account-book/bookkeeping';

import { onMounted, ref } from 'vue';

import { getAuxiliary, getLedger, getSummary } from '#/api/account-book/bookkeeping';

const abstractselectdata = ref<SummaryItmData>([]);
const subjectselectdata = ref<SummaryItmData>([]);
const auxiliaryselectdata = ref<AuxiliaryItm[]>([]);
// 摘要数据
export const useAbstractData = () => {
    const fetchData = async () => {
        const res = await getSummary();
        if (res.returnCode === '200') {
            abstractselectdata.value = res.data;
        }
    };
    onMounted(() => {
        if (abstractselectdata.value.length === 0) {
            fetchData();
        }
    });
    return {
        selectdata: abstractselectdata,
        fetchData,
    };
};
// 科目数据
export const useSubjectData = () => {
    const fetchData = async () => {
        const res = await getLedger();
        if (res.returnCode === '200') {
            subjectselectdata.value = res.data;
        }
    };
    onMounted(() => {
        if (subjectselectdata.value.length === 0) {
            fetchData();
        }
    });
    return {
        selectdata: subjectselectdata,
        fetchData,
    };
};
// 供应商公司数据
export const useAuxiliaryData = () => {
    const fetchData = async () => {
        const res = await getAuxiliary();
        if (res.returnCode === '200') {
            auxiliaryselectdata.value = res.data;
        }
    };
    onMounted(() => {
        if (auxiliaryselectdata.value.length === 0) {
            fetchData();
        }
    });
    return {
        selectdata: auxiliaryselectdata,
        fetchData,
    };
};
