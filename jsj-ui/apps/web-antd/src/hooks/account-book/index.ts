import { onMounted, ref } from 'vue';

import { getAssetType, getForeignCurrencyType } from '#/api/account-book/index';

const foreignCurrencyType = ref<any>([]);
const assetType = ref<any>([]);
// 外币类型数据
export const useForeignCurrencyType = () => {
    const fetchData = async () => {
        const res = await getForeignCurrencyType();
        if (res.returnCode === '200') {
            foreignCurrencyType.value = res.data;
        }
    };
    onMounted(() => {
        if (foreignCurrencyType.value.length === 0) {
            fetchData();
        }
    });
    return {
        data: foreignCurrencyType,
        fetchData,
    };
};
// 资产类型数据
export const useAssetType = () => {
    const fetchData = async () => {
        if (assetType.value.length > 0) {
            return;
        }
        const res = await getAssetType();
        if (res.returnCode === '200') {
            assetType.value = res.data;
        }
    };
    onMounted(() => {
        if (assetType.value.length === 0) {
            fetchData();
        }
    });
    return {
        data: assetType,
        fetchData,
    };
};
