import { ref } from 'vue';

type Loading = {
    size?: string; // 字符大小
    text?: string; // 显示的文字
    time?: number; // 多少秒后关闭
};
const globalLoadingShow = ref<boolean>(false);
const globalLoadingData = ref<Loading | undefined>();
let globalLoadingTime: any = null;
export const useGlobalLoading = () => {
    const setShow = (type: boolean, data?: Loading) => {
        globalLoadingShow.value = type;
        if (type && data?.time) {
            globalLoadingTime && clearTimeout(globalLoadingTime);
            globalLoadingTime = setTimeout(() => {
                globalLoadingShow.value = false;
                globalLoadingData.value = undefined;
            }, data.time * 1000);
        }
        if (type && data) {
            globalLoadingData.value = data;
        }
        if (!type) {
            globalLoadingData.value = undefined;
        }
    };
    return {
        isShow: globalLoadingShow,
        data: globalLoadingData,
        setShow,
    };
};
