import type { Dayjs } from 'dayjs';

import dayjs from 'dayjs';
/**
 * 将"YYYY-M"格式的日期字符串转换为季度
 * @param dateStr - 格式为"YYYY-M"的日期字符串，如"2025-1"
 * @returns 对应的季度，格式为"YYYY-QN"，如"2025-Q1"
 */
export function convertToQuarter(dateStr: string): string {
    if (!dateStr) return '';

    // 分割字符串，提取年份和月份
    const [yearStr, monthStr] = dateStr.split('-');
    if (!yearStr || !monthStr) {
        console.error('Invalid date format:', dateStr);
        return '';
    }
    const year = Number.parseInt(yearStr);
    const month = Number.parseInt(monthStr);

    // 确保月份有效
    if (Number.isNaN(month) || month < 1 || month > 12) {
        console.error('Invalid month:', month);
        return '';
    }

    // 计算季度
    const quarter = Math.ceil(month / 3);

    // 返回格式化的季度字符串
    return `${year}-Q${quarter}`;
}

/**
 * 将季度字符串转换为 Day.js 对象
 * @param quarterStr - 格式为"YYYY-QN"的季度字符串，如"2025-Q1"
 * @returns 对应的 Day.js 对象
 */
export function quarterToDayjs(quarterStr: string): dayjs.Dayjs | undefined {
    if (!quarterStr) return undefined;

    const parts = quarterStr.split('-Q');
    if (parts.length !== 2) {
        console.error('Invalid quarter format:', quarterStr);
        return undefined;
    }
    const [year, quarter] = parts;
    if (!year || !quarter) {
        console.error('Invalid quarter format:', quarterStr);
        return undefined;
    }
    const yearNum = Number.parseInt(year);
    const quarterNum = Number.parseInt(quarter);

    if (Number.isNaN(yearNum) || Number.isNaN(quarterNum) || quarterNum < 1 || quarterNum > 4) {
        console.error('Invalid quarter format:', quarterStr);
        return undefined;
    }

    // 计算季度开始的月份
    const month = (quarterNum - 1) * 3;

    // 创建 Day.js 对象
    return dayjs().year(yearNum).month(month).date(1);
}

/**
 * 组合函数：将"YYYY-M"格式转换为Ant Design季度选择器的值
 */
export function dateStrToQuarterPickerValue(dateStr: string): dayjs.Dayjs | undefined {
    const quarterStr = convertToQuarter(dateStr);
    return quarterToDayjs(quarterStr);
}
