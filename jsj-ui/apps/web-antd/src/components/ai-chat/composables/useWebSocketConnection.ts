import { computed, ref } from 'vue';
import { useWebSocket } from '@vueuse/core';
import { message } from 'ant-design-vue';
import type { ConnectionStatus, WebSocketMessage } from '../types/chat';

export function useWebSocketConnection(
  wsUrl: string,
  messageHandler?: (event: MessageEvent) => void,
) {
  const taskProgressMap = new Map();

  // 存储连接成功后的回调
  let onConnectedCallback: (() => void) | null = null;

  const {
    close: wsClose,
    open: wsOpen,
    send: wsSend,
    status: wsStatus,
  } = useWebSocket(wsUrl, {
    autoReconnect: {
      delay: 3000, // 重连延迟 3 秒
      onFailed() {
        console.error('WebSocket重连失败，已达到最大重试次数');
        message.error('网络连接异常，请刷新页面重试');
      },
      retries: 5, // 最大重试次数
    },
    heartbeat: {
      interval: 30_000, // 每30秒发送一次心跳
      message: JSON.stringify({ timestamp: Date.now(), type: 'ping' }), // 心跳消息
      pongTimeout: 10_000, // 等待响应超时时间10秒
      // 心跳响应验证
      responseMessage: 'pong',
    },
    immediate: false, // 禁用自动连接，需要手动调用 wsOpen()
    onConnected: () => {
      console.log('WebSocket连接成功');
      message.success('连接成功');
      // 执行连接成功后的回调
      if (onConnectedCallback) {
        onConnectedCallback();
      }
    },
    onDisconnected: (_, event) => {
      console.log('WebSocket连接断开', event);
      // 只有在非正常关闭时才显示断开提示
      if (event.code !== 1000) {
        message.warning('连接已断开，正在尝试重连...');
      }
    },
    onError: (_, event) => {
      console.error('WebSocket连接错误:', event);
      message.error('WebSocket连接失败，正在尝试重连...');
    },
    onMessage: messageHandler ? (_, event) => messageHandler(event) : undefined,
  });

  const connectionStatus = computed<ConnectionStatus>(() => ({
    status: wsStatus.value as ConnectionStatus['status'],
    isConnected: wsStatus.value === 'OPEN',
    isConnecting: wsStatus.value === 'CONNECTING',
    isClosed: wsStatus.value === 'CLOSED',
  }));

  // 发送更新客户端信息的消息
  const sendUpdateClientInfoMessage = (companyName: string, month: string) => {
    if (!companyName) return;

    const updateClientInfoMessage = {
      company_name: companyName,
      month,
      type: 'update_client_info',
    };

    const jsonStr = JSON.stringify(updateClientInfoMessage);
    console.log('发送更新客户端信息消息:', jsonStr);
    wsSend(jsonStr);
  };

  // 发送消息（包含文件）
  const sendMessageWithFiles = (
    content: string,
    files: Array<{ location_type: string; url: string }>,
    companyName: string,
    month: string,
  ) => {
    const messageData = {
      client_type: 'ai',
      data: {
        company_name: companyName,
        files,
        month,
      },
      task_type: 'multi_file_processing',
      type: 'task_request',
    };

    console.log('发送的消息数据:', messageData);
    console.log('文件数量:', files.length);

    const jsonStr = JSON.stringify(messageData);
    console.log('Valid JSON sent:', jsonStr);
    wsSend(jsonStr);
  };

  // 设置连接成功后的回调
  const setOnConnectedCallback = (callback: () => void) => {
    onConnectedCallback = callback;
  };

  // 手动重连WebSocket
  const reconnectWebSocket = () => {
    console.log('手动重连WebSocket...');
    message.loading('正在重连...', 1);
    wsClose();
    setTimeout(() => {
      wsOpen();
    }, 1000);
  };

  return {
    wsClose,
    wsOpen,
    wsSend,
    wsStatus,
    connectionStatus,
    taskProgressMap,
    sendUpdateClientInfoMessage,
    sendMessageWithFiles,
    reconnectWebSocket,
    setOnConnectedCallback,
  };
}
