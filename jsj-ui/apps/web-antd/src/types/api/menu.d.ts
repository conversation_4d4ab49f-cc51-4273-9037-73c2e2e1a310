export interface childItm {
    showName: string;
    code: string;
    param: string;
    name: string;
    icon: string;
    id: string;
    type: 'DIR' | 'HTML';
    isLeaf: boolean;
    url: string;
    parentId: string;
    isShow: boolean;
}
export interface MenuItm {
    code: string;
    id: string;
    isLeaf: boolean;
    isShow: boolean;
    name: string;
    showDirection: string;
    showName: string;
    type: string;
    url: string;
    childs?: childItm[];
}
