import { inject, ref } from 'vue';

import { message, Modal } from 'ant-design-vue';

import { useGlobalLoading } from '#/hooks/useGlobalLoading';

export function useRequest<T = any>() {
    // 获取全局 loading
    const { setShow } = useGlobalLoading();
    /**
     * @param apiFn 具体的接口函数
     * @param params 接口参数
     * @param successMsg 成功提示（可选）
     * @returns 接口返回数据
     */
    const run = async (apiFn: (...args: any[]) => Promise<any>, params?: any, successMsg?: string): Promise<T | undefined> => {
        setShow(true);
        try {
            const res = await apiFn(params);
            setShow(false);
            if (res.returnCode === '200') {
                if (successMsg) message.success(successMsg);
                return res;
            } else {
                Modal.info({
                    title: '对话框',
                    content: `${res.returnCode}: ${res.returnMsg || '操作失败'}`,
                });
                return undefined;
            }
        } catch (error: any) {
            setShow(false);
            message.error(error?.message || '请求失败');
            return undefined;
        }
    };

    return { run };
}
