type BookTypeItm = {
    label?: string;
    value: string;
};
// 凭证类型
const VoucherType: BookTypeItm[] = [
    {
        value: 'All',
        label: '全部',
    },
    {
        value: 'NORMAL',
        label: '普通凭证',
    },
    {
        value: 'INVOICE',
        label: '发票凭证',
    },
    {
        value: 'FIXED_ASSET',
        label: '固定资产凭证',
    },
];
// 账本类型
const bookTypes: BookTypeItm[] = [
    {
        value: '记',
    },
    {
        value: '收',
    },
    {
        value: '付',
    },
    {
        value: '转',
    },
    {
        value: '现收',
    },
    {
        value: '现付',
    },
    {
        value: '银收',
    },
    {
        value: '银付',
    },
];
// 辅助核算的类型
const auxiliaryAccounting: BookTypeItm[] = [
    {
        value: 'All',
        label: '类型',
    },
    {
        value: 'Supplier',
        label: '供应商',
    },
    {
        value: 'Customer',
        label: '客户',
    },
    {
        value: 'Project',
        label: '项目',
    },
    {
        value: 'Department',
        label: '部门',
    },
    {
        value: 'Person',
        label: '人员',
    },
    {
        value: 'Stock',
        label: '存货',
    },
];
// 服务类型
const serviceType: BookTypeItm[] = [
    {
        value: '全部服务类型',
    },
    {
        value: '正常服务员',
    },
    {
        value: '停止服务',
    },
];
export const commondata = {
    voucherType: VoucherType,
    bookTypes,
    auxiliaryAccounting,
    serviceType,
};
